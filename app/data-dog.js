import React from 'react';

import { cookies } from 'next/headers';
import Script from 'next/script';

const DataDogScript = ({ session }) => {
  const organizationId = cookies()?.get('aceprepTenantId')?.value;
  let organization;
  let role;
  let organizationName;
  // if user logs in for first time
  if (!organizationId) {
    organization = session?.memberships?.[0]?.organizationId;
    role = session?.memberships?.[0]?.role;
    organizationName = session?.memberships?.[0]?.organization?.name;
  }
  organization = organizationId;
  const memberships = session?.memberships;
  if (memberships) {
    for (const membership of memberships) {
      if (membership?.organizationId === organizationId) {
        role = membership?.role;
        organizationName = membership?.organization?.name;
        break;
      }
    }
  }
  return (
    <Script id="datadog-rum" strategy={'afterInteractive'}>
      {`
      (function(h,o,u,n,d) {
        h=h[d]=h[d]||{q:[],onReady:function(c){h.q.push(c)}}
        d=o.createElement(u);d.async=1;d.src=n
        n=o.getElementsByTagName(u)[0];n.parentNode.insertBefore(d,n)
      })(window,document,'script','https://www.datadoghq-browser-agent.com/us1/v5/datadog-rum.js','DD_RUM');
      (function(h,o,u,n,d) {
        h=h[d]=h[d]||{q:[],onReady:function(c){h.q.push(c)}}
        d=o.createElement(u);d.async=1;d.src=n
        n=o.getElementsByTagName(u)[0];n.parentNode.insertBefore(d,n)
      })(window,document,'script','https://www.datadoghq-browser-agent.com/us1/v5/datadog-logs.js','DD_LOGS')
      window.DD_RUM.onReady(function() {
        window.DD_RUM.init({
          clientToken: '${process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN}',
          applicationId: '${process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID}',
          site: '${process.env.NEXT_PUBLIC_DATADOG_SITE}',
          service: '${process.env.NEXT_PUBLIC_DATADOG_SERVICE}',
          env: '${process.env.NEXT_PUBLIC_ENVIRONMENT}',
          // Specify a version number to identify the deployed version of your application in Datadog
          // version: '1.0.0',
          sessionSampleRate: 100,
          sessionReplaySampleRate: 100,
          trackUserInteractions: true,
          trackResources: true,
          trackLongTasks: true,
          defaultPrivacyLevel: 'mask-user-input',
        });
        window.DD_RUM.setUser({
          id: '${session?.userId}',
          name: '${session?.user?.name}',
          email: '${session?.user?.email}',
          organization: '${organization}',
          organizationName: '${organizationName}',
          role: '${role}',
        });
      })
      window.DD_LOGS.onReady(function() {
        window.DD_LOGS.init({
          clientToken: '${process.env.NEXT_PUBLIC_DATADOG_CLIENT_TOKEN}',
          applicationId: '${process.env.NEXT_PUBLIC_DATADOG_APPLICATION_ID}',
          site: '${process.env.NEXT_PUBLIC_DATADOG_SITE}',
          service: '${process.env.NEXT_PUBLIC_DATADOG_SERVICE}',
          env: '${process.env.NEXT_PUBLIC_ENVIRONMENT}',
          forwardErrorsToLogs: true,
          sessionSampleRate: 100,
        })
      })
    `}
    </Script>
  );
};

export default DataDogScript;
