import React from 'react';

import { Metadata } from 'next';
import localFont from 'next/font/local';
import Head from 'next/head';
import { cookies } from 'next/headers';
import Script from 'next/script';

import { Meta } from '@/components/meta';
import Providers from '@/components/provider/provider';
import SupportWidget from '@/components/support-widget';
import { authOptions } from '@/lib/auth';
import '@/styles/globals.css';
import { Analytics } from '@vercel/analytics/react';
import { getServerSession } from 'next-auth';
import { Toaster } from 'react-hot-toast';

import DataDogScript from './data-dog';

const sathosi = localFont({ src: './font/satoshi.otf' });

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? 'Flinkk - Hire' : 'CampEd - Aceprep',
  description:
    'Ace Your Interviews with AI Coaching, Mock Interviews, and Question BankAcePrep: Elevate your interview skills with our cutting-edge video assessment platform. Practice mock interviews with real-world questions, receive personalized feedback from AI on key metrics like communication, body language, and content. Sharpen your interview techniques, boost your confidence, and land your dream job with AcePrep.',
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_PLATFORM === 'hire'
      ? 'https://hire.flinkk.io'
      : 'https://aceprep.camped.academy',
  ),
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const session = await getServerSession(authOptions);
  const organization = (session as any)?.memberships;
  const organizationId = cookies()?.get('aceprepTenantId')?.value;

  return (
    <html lang="en">
      <Head>
        <Meta />
        <link
          rel="icon"
          href={
            process.env.NEXT_PUBLIC_PLATFORM === 'hire'
              ? '/favicon_hire.ico'
              : '/favicon_aceprep.ico'
          }
          sizes="any"
        />
      </Head>

      <Script src="https://checkout.razorpay.com/v1/checkout.js"></Script>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_TRACKING_ID}`}
      />
      {/* Support widget is now conditionally rendered based on route */}
      <SupportWidget />
      <Script
        id="clarity"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
          !function(c,l,a,r,i,t,y) {
            if (typeof a[c] !== 'function') {
              a[c] = function() {
                (a[c].q = a[c].q || []).push(arguments)
              };
            }
            if (a[c].v || a[c].t) {
              return a[c]("event", c, "dup." + i.projectId);
            }
            a[c].t = true;
            t = l.createElement(r);
            t.async = true;
            t.src = "https://www.clarity.ms/s/0.7.59/clarity.js";
            y = l.getElementsByTagName(r)[0];
            y.parentNode.insertBefore(t, y);
            a[c]("start", i);
            a[c].q.unshift(a[c].q.pop());
            a[c]("set", "C_IS", "0");
            a[c]("set", "C_V", "v_longTaskControl");
          }("clarity", document, window, "script", {
            "projectId": "${process.env.NEXT_PUBLIC_CLARITY_ID}",
            "upload": "https://r.clarity.ms/collect",
            "expire": 365,
            "cookies": ["_uetmsclkid", "_uetvid"],
            "track": true,
            "content": true,
            "dob": 1836,
            "longTask": 30
          });
        `,
        }}
      />

      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
          `,
        }}
      />
      <Meta />
      <link
        rel="icon"
        href={
          process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? '/favicon_hire.ico' : '/favicon_aceprep.ico'
        }
        sizes="any"
      />
      <body className={sathosi.className}>
        <Providers
          attribute="class"
          defaultTheme="light"
          enableSystem
          session={session}
          organization={organization}
          organizationId={organizationId}
        >
          {children}
          <DataDogScript session={session} />
          <Toaster position="top-center" reverseOrder={false} />
          <Analytics />
        </Providers>
      </body>
    </html>
  );
}
