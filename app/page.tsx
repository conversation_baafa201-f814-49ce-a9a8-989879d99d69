import { redirect } from 'next/navigation';

import Header from '@/components/Header';
import Footer from '@/components/footer';
import FeaturesHire from '@/components/home-hire/features';
import HeroHire from '@/components/home-hire/hero';
import Advertise from '@/components/home/<USER>';
import Benefits from '@/components/home/<USER>';
import Commuity from '@/components/home/<USER>';
import Features from '@/components/home/<USER>';
import Hero from '@/components/home/<USER>';
import Resume from '@/components/home/<USER>';
import RoleSelectSection from '@/components/section/role-select-section';
import { authOptions } from '@/lib/auth';
import { getLevel, getRole } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Home() {
  const session = await getServerSession(authOptions);

  if (session?.userId) {
    return redirect('/dashboard');
  }
  const role = await getRole();
  const level = await getLevel();
  if (process.env.NEXT_PUBLIC_PLATFORM === 'hire')
    return (
      <>
        <Header data={session} />
        <HeroHire />
        <FeaturesHire />
        <Footer />
        {/* <GetStarted /> */}
      </>
    );
  return (
    <>
      <Header data={session} />
      <Hero />

      <div className="bg-secondary">
        <Resume session={session} />
      </div>

      <RoleSelectSection role={role} level={level} userId={session?.userId} />
      <Features />
      <Benefits />
      <Advertise />
      <Commuity />
      <Footer />
      {/* <GetStarted /> */}
    </>
  );
}
