import { redirect } from 'next/navigation';

import { PricingScreen } from '@/components/wrapper-screen/pricing-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function PricingPage() {
  const session: any = await getServerSession(authOptions);
  if (process.env.NEXT_PUBLIC_PLATFORM === 'hire' || session?.memberships?.length > 0 || !session) {
    redirect('/404');
  }
  const fetchPlans = async () => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/lookups/pricingPlans`,
    );
    if (response.ok) {
      const data = await response.json();
      return data?.items;
    } else {
      throw new Error('Failed to fetch plans.');
    }
  };

  const fetchPlanData = fetchPlans();
  const checkSubscription = async () => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/getSubscriptionStatus`,
    );
    if (response.ok) {
      const { plan, status, stripeCurrentPeriodEnd } = await response.json();
      return {
        plan,
        status,
      };
    } else {
      throw new Error('Failed to fetch subscription status.');
    }
  };

  const planData = checkSubscription();
  return (
    <PricingScreen
      session={session}
      pricePlan={await fetchPlanData}
      palnApi={(await planData).plan}
    />
  );
}
