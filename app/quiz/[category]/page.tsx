import { cookies } from 'next/headers';

import { CategoryScreen } from '@/components/wrapper-screen/quiz-category-screen';
import { authOptions } from '@/lib/auth';
import { getQuizTopics } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage(props) {
  const category = props?.params?.category;
  const session = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const quiz = await getQuizTopics(category);
  return <CategoryScreen quiz={quiz} category={category} tenantId={tenantId} session={session} />;
}
