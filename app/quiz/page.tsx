import { cookies } from 'next/headers';

import { QuizScreen } from '@/components/wrapper-screen/quiz-screen';
import { authOptions } from '@/lib/auth';
import { getCompany, getQuizCategory } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function DemoPage() {
  const session = await getServerSession(authOptions);
  const tenantId = cookies().get('aceprepTenantId')?.value;
  const [quiz, company] = await Promise.all([getQuizCategory(), getCompany()]);

  return <QuizScreen quiz={quiz} company={company} session={session} tenantId={tenantId} />;
}
