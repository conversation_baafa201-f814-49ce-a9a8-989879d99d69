import React from 'react';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import Resume from '@/components/home/<USER>';
import { authOptions } from '@/lib/auth';
import { getToken } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Home() {
  const session = await getServerSession(authOptions);

  const sessionUserId = session?.userId;
  const token = sessionUserId ? await getToken() : null;

  const url = token ? `${process.env.NEXT_PUBLIC_RESUME_URL}?token=${token}` : '';
  if (!url) {
    return <Resume session={session} />;
  }
  return (
    <iframe
      src={url}
      width="100%"
      style={{ height: 'calc(100vh - 120px)' }}
      allow="clipboard-read; clipboard-write"
    ></iframe>
  );
}
