import { redirect } from 'next/navigation';

import { SignInScreen } from '@/components/wrapper-screen/sign-in-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function SignIn(props) {
  const source = props?.params?.source || '';
  const theme = props?.params?.theme || 'light';
  const callbackUrl = props?.params?.from || 'dashboard';

  const token = props?.params?.token || props?.searchParams?.token || '';
  const email = props?.params?.email || props?.searchParams?.email;
  const session = await getServerSession(authOptions);

  if (!session?.userId && token && email) {
    const finalCallbackUrl = encodeURIComponent(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/${callbackUrl}?theme=${theme}&source=${source}`,
    );
    const url = `/api/auth/callback/email?callbackUrl=${finalCallbackUrl}&token=${token}&email=${email}`;
    return redirect(url);
  }

  if (session?.userId) {
    if (callbackUrl) {
      return redirect(`/${callbackUrl}?theme=${theme}&source=${source}`);
    } else {
      return redirect('/dashboard');
    }
  }

  return <SignInScreen source={source} session={session} callbackUrl={callbackUrl} />;
}
