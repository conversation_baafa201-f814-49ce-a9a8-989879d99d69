import Link from 'next/link';

import { AuthLayout } from '@/components/authLayout';
import { UserAuthForm } from '@/components/form/user-auth-form';
import { AppLogo } from '@/layout/logo';

export default function SignUp() {
  return (
    <AuthLayout>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <AppLogo />
          <h1 className="text-2xl font-semibold tracking-tight">Sign Up</h1>
          <p className="text-sm text-muted-foreground">Enter your email to sign up</p>
        </div>
        <UserAuthForm />
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link href="/sign-in" className="hover:text-brand underline underline-offset-4">
            Already have an account? Sign In
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}
