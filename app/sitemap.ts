// import { getSbStory } from "@/lib/storyblok/getStories";
import { MetadataRoute } from 'next';

import axios from 'axios';
import globby from 'globby';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const pages = await globby([
    'pages/*.tsx',
    'pages/*/*.tsx',
    'data/**/*.mdx',
    '!data/*.mdx',
    '!pages/_*.tsx',
    '!pages/api',
    '!pages/404.tsx',
    '!pages/profile.tsx',
    '!pages/interview-results.tsx',
    '!pages/mock/my-practice.tsx',
    '!pages/auth.tsx',
    '!pages/questions.tsx',
    '!pages/mock.tsx',
    '!pages/blog/[...*].tsx',
    '!pages/category/*.tsx',
    '!pages/tag/*.tsx',
    '!pages/mock.tsx',
    '!pages/organization/*.tsx',
    '!pages/institution/*.tsx',
    '!pages/update-profile.tsx',
    '!pages/tailored-practice/my-practice.tsx',
    '!pages/career-practice-result.tsx',
    '!pages/interview-profile.tsx',
  ]);
  const urls: MetadataRoute.Sitemap = [];
  pages.map((page) => {
    const path = page
      .replace('pages', '')
      .replace('data', '')
      .replace('.tsx', '')
      .replace('.mdx', '');

    const excludedPaths = [/^\/[^/]*\/index$/]; // Regular expressions to match paths like /blogs/index, /category/index, etc.

    const isExcluded = excludedPaths.some((regex) => regex.test(path));

    let route = path;
    const baseURL =
      process.env.NEXT_PUBLIC_PLATFORM === 'hire'
        ? 'https://hire.flinkk.io'
        : 'https://aceprep.camped.academy';

    if (path === '/index') {
      route = '';
    } else if (isExcluded) {
      route = path.replace(/\/index$/, '');
    } else {
      route = path;
    }

    urls.push({
      url: `${baseURL}${route}`,
      lastModified: new Date(),
    });
    return;
  });
  try {
    const response = await axios.get(
      `https://mapi.storyblok.com/v1/spaces/247209/stories?is_published=true&starts_with=blog/`,
      {
        headers: {
          Authorization: 'hloKQOYkuSvsxdN92mXDpQtt-108008-QyubquhWZiu_XmzQysRh',
        },
      },
    );
    const baseURL =
      process.env.NEXT_PUBLIC_PLATFORM === 'hire'
        ? 'https://hire.flinkk.io'
        : 'https://aceprep.camped.academy';
    response.data.stories?.map(({ full_slug }) => {
      urls.push({
        url: `${baseURL}/${full_slug}`,
        lastModified: new Date(),
      });
    });
  } catch (error) {
    console.log({ error });
  }

  return urls;
}
