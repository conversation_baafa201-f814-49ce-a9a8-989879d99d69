import { cookies } from 'next/headers';

import Header from '@/components/Header';

export default async function Layout({ children }) {
  const userId = cookies().get('aceprepUserId')?.value;
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div className={`relative h-full min-h-screen w-full overflow-x-hidden`}>
        <Header data={{ userId }} />
        <div className="flex w-full flex-col md:flex-row">
          <div className="flex min-h-[60vh] w-full flex-col items-center justify-center px-4 pb-8 pt-2 md:px-0 md:py-2">
            <div className="mt-[100px] flex w-full flex-col items-center">
              <div className="flex flex-col space-y-2 text-center">
                <h2
                  className={`mt-[100px] text-2xl font-bold leading-tight tracking-tighter sm:text-3xl md:text-5xl lg:text-6xl`}
                >
                  Support
                </h2>
              </div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
