'use client';

import React from 'react';

import { useRouter } from 'next/navigation';

import RichTextEditor from '@/lib/rich-text-editor';
import { createSupport } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { buttonVariants } from '@camped-ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';
import { cn } from '@camped-ui/lib';

export default function SupportForm({}) {
  const router = useRouter();
  const userId = getCookie('aceprepUserId');
  const organizationId = getCookie('aceprepTenantId');
  if (!userId) {
    router.push('/404');
  }
  const form = useForm({
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });
  const handleSubmit = async (data) => {
    toast.loading('Rising your ticket...');
    const ticket = await createSupport({ ...data, organizationId, createdById: userId });
    toast.remove();
    if (ticket?.support) {
      toast.success('Ticket raised successfully');
      form.reset();
    } else {
      toast.error('Failed to raise ticket,Please try again');
    }
  };
  return (
    <Form {...(form as any)} className="h-full w-full">
      <form
        onSubmit={form?.handleSubmit(handleSubmit)}
        className="h-full w-full px-4 lg:max-w-[40%]"
      >
        <div className="grid gap-2">
          <FormField
            control={form.control as any}
            name="name"
            render={({ field }) => {
              return (
                <FormItem className="grid gap-1">
                  <FormLabel htmlFor="name" className="">
                    Name
                  </FormLabel>
                  <FormControl className="">
                    <Input
                      id="name"
                      placeholder="Your name"
                      type="text"
                      autoCapitalize="none"
                      autoComplete="name"
                      autoCorrect="off"
                      {...field}
                      required
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control as any}
            name="email"
            render={({ field }) => {
              return (
                <FormItem className="grid gap-1">
                  <FormLabel htmlFor="email" className="">
                    Email
                  </FormLabel>
                  <FormControl className="">
                    <Input
                      id="email"
                      placeholder="<EMAIL>"
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      required
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            control={form.control as any}
            name="message"
            rules={{
              required: 'Message required',
            }}
            render={({ field }) => {
              return (
                <FormItem className="h-full gap-1">
                  <FormLabel htmlFor="message" className="">
                    Message
                  </FormLabel>
                  <FormControl className="h-full">
                    <RichTextEditor {...field} setInfoOpen={() => {}} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <button className={cn(buttonVariants(), 'mt-32')} type="submit">
            Submit
          </button>
        </div>
      </form>
    </Form>
  );
}
