import { cookies } from 'next/headers';

import RoleSelectSection from '@/components/section/role-select-section';
import { getLevel, getRole } from '@/services/apicall';

export default async function Home() {
  const userId = cookies()?.get('aceprepUserId')?.value;

  const role = await getRole();
  const level = await getLevel();
  return <RoleSelectSection role={role} level={level} userId={userId} />;
}
