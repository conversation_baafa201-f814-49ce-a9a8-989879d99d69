import Header from '@/components/Header';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function Layout({ children }) {
  const session = await getServerSession(authOptions);
  return (
    <main className={`relative flex h-full w-full flex-1 flex-col overflow-hidden`}>
      <div
        className={`relative h-full min-h-screen w-full overflow-x-hidden`}
        style={{ paddingBottom: 88 }}
      >
        <Header data={session} showMenus={false} />
        <div className="container mx-auto mt-[85px] flex w-full flex-col gap-4">{children}</div>
      </div>
    </main>
  );
}
