import { redirect } from 'next/navigation';

import Header from '@/components/Header';
import { ViewEventsTable } from '@/components/data-table/view-events/data-table';
import { authOptions } from '@/lib/auth';
import { CampedTableProvider } from '@/packages/shared-data-table/camped-table-provider';
import { getPublicInterviewList } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function InterViewEvent(props) {
  const eventId = props.searchParams.id;
  const searchParams = props?.searchParams;

  const interviewResult = await getPublicInterviewList({
    id: eventId,
    searchParams,
  });

  if (!interviewResult?.items) return redirect('/404');
  return (
    <CampedTableProvider>
      <ViewEventsTable
        tasksPromise={{
          data: interviewResult,
        }}
        eventId={eventId}
      />
    </CampedTableProvider>
  );
}
