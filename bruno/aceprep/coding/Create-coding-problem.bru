meta {
  name: Create coding problem
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/dev/coding/create
  body: json
  auth: none
}

body:json {
  {
    "problem": {
      "title": "title",
      "description": "description",
      "time": 25,
      "slug": "slug",
      "difficulty": "MEDIUM",
      "status": "PUBLISHED",
      "categories": [
        {
          "name": "Data Structure",
          "slug": "data-structure",
          "type": "CATEGORY"
        }
      ],
      "tags": [
        {
          "name": "Functions",
          "slug": "functions",
          "type": "TAG"
        }
      ],
      "template": [
        {
          "code": "ZGVmIGNvdW50X2J5KGFycmF5LCBpdGVyYXRlZSk6CiAgICAjIFRPRE86IEltcGxlbWVudCB0aGUgY291bnRCeSBmdW5jdGlvbiBsb2dpYyBoZXJlCiAgICBwYXNzCgojIEV4YW1wbGUgdXNhZ2U6CiMgcmVzdWx0ID0gY291bnRfYnkoWzEsIDIsIDMsIDQsIDVdLCBsYW1iZGEgbnVtOiBOb25lKQojIHByaW50KHJlc3VsdCkKCiMgRXhhbXBsZSB1c2FnZSB3aXRoIHN0cmluZyBpdGVyYXRlZToKIyByZXN1bHRfc3RyaW5nID0gY291bnRfYnkoWydhcHBsZScsICdiYW5hbmEnLCAnb3JhbmdlJ10sIGxlbikKIyBwcmludChyZXN1bHRfc3RyaW5nKQo=",
          "languageId": 71,
          "languageName": "python"
        }
      ]
    }
  }
}
