meta {
  name: create frontend problem
  type: http
  seq: 1
}

post {
  url: {{base_url}}/api/dev/frontend/create
  body: json
  auth: none
}

body:json {
  {
    "problem": {
      "title": "title",
      "description": "description",
      "markdown": "mark down",
      "time": 30,
      "slug": "slug",
      "difficulty": "MEDIUM",
      "framework": "framework",
      "status": "PUBLISHED",
      "template": {
        "files": [
          {
            "fileData": "",
            "fileName": "/index.js"
          },
          {
            "fileData": "",
            "fileName": "/style.css"
          },
          {
            "fileData": "",
            "fileName": "/App.js"
          }
        ],
        "environment": "create-react-app",
        "dependencies": {
          "react": "^18.1.0",
          "react-dom": "^18.1.0"
        },
        "templateName": "react"
      }
    }
  }
}
