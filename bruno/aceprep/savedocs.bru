meta {
  name: savedocs
  type: http
  seq: 3
}

post {
  url: http://localhost:8001/api/method/frappe.desk.form.save.savedocs
  body: multipartForm
  auth: none
}

headers {
  token: token 8a35388759c5b38:b6dbd6300533ae0
}

body:multipart-form {
  doc: {"docstatus":0,"doctype":"Job Opening","name":"new-job-opening-kcbqiqsnyw","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","posted_on":"2025-01-03 08:38:15","company":"Camped","publish":0,"publish_applications_received":1,"custom_interview_rounds":[],"currency":"INR","salary_per":"Month","publish_salary_range":0,"designation":"Administrative Assistant","staffing_plan":"","planned_vacancies":0,"job_title":"Test 2"}
  action: Save
}
