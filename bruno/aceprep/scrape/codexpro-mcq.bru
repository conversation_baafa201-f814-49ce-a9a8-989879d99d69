meta {
  name: codexpro-mcq
  type: http
  seq: 4
}

get {
  url: https://codexpro.ai/api/assessments/questions?page_no=1&page_size=1000&question_type=2
  body: none
  auth: none
}

params:query {
  page_no: 1
  page_size: 1000
  question_type: 2
}

headers {
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzMyMjcyOTc4LCJqdGkiOiI4ZGZkOWNiODFhMWQ0MTM3YjlhMWQyNmEyOGY3NjY2MiIsInVzZXJfaWQiOjEwNTc1fQ.Jq3iRGDbFNj6vQBHR3h60l-JXNhTj3bTrfJBQIZ95mo
  referer: https://codexpro.ai/codexpro/dashboard/questions?tab=2&pageNo=2
}
