'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

import { useMembershipStore } from '@/packages/shared-store';
import { getCookie, removeCookie, setCookie } from '@/utils/cookies';
import { signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Button } from '@camped-ui/button';
import { Card } from '@camped-ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';

import { Icon } from '../../icons';
import OrgHeader from './org';
import PublicHeader from './public';

const Header = ({ data, showMenus = true }) => {
  const pathname = usePathname() || '';

  const [menuOpen, setMenuOpen] = useState(false);

  const updateMembership = useMembershipStore((state) => (state as any).update);
  const nonHeaderPath = ['/sign-in', '/sign-up'];

  useEffect(() => {
    const tenantIdString = getCookie('aceprepTenantId');
    const userIdString = getCookie('aceprepUserId');
    const tenantId =
      tenantIdString === 'null' || tenantIdString === 'undefined' ? null : tenantIdString;
    const LocalUserId =
      userIdString === 'null' || userIdString === 'undefined' ? null : userIdString;

    const userId = data?.userId;

    if (data?.memberships?.length !== 0 && (LocalUserId !== userId || !LocalUserId || !tenantId)) {
      const initialMembership = data?.memberships?.[0];
      if (initialMembership) {
        const {
          organization: { id },
        } = initialMembership;
        if (id) {
          setCookie('aceprepTenantId', id, {
            domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
          });
          updateMembership(id, data?.memberships);
        }
        if (userId)
          setCookie('aceprepUserId', userId, {
            domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
          });
      }
    } else if (data?.userId && data?.memberships?.length === 0) {
      removeCookie('aceprepTenantId');
      if (userId)
        setCookie('aceprepUserId', userId, {
          domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,
        });
    }
  }, [data]);

  if (nonHeaderPath.includes(pathname)) {
    return null;
  }

  if (!data?.userId) {
    return <PublicHeader />;
  }

  return (
    <OrgHeader data={data} menuOpen={menuOpen} setMenuOpen={setMenuOpen} showMenus={showMenus} />
  );
};

export const UserDropDown = ({ user, name }) => {
  const [openPopover, setOpenPopover] = useState(false);
  const router = useRouter();
  const { theme } = useTheme();

  if (!user) {
    return <Button onClick={() => router.push('/sign-in')}>SIGN IN</Button>;
  }
  return (
    <div className="relative inline-block text-left">
      <Popover open={openPopover} onOpenChange={() => setOpenPopover(!openPopover)}>
        <PopoverTrigger asChild>
          <button onClick={() => setOpenPopover(!openPopover)}>
            <Card className="h-10 w-10 rounded-md bg-secondary">
              <Avatar className="h-9 w-9 rounded-md">
                <AvatarImage
                  src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${user?.id}?date=${user?.updateAt}`}
                  alt="@camped"
                />
                <AvatarFallback className="rounded">
                  <div className="flex h-16 w-16 items-center justify-center">
                    <Icon name="profile" color={theme === 'dark' ? '#fff' : '#000'} />
                  </div>
                </AvatarFallback>
              </Avatar>
            </Card>
          </button>
        </PopoverTrigger>
        <PopoverContent className="z-[10000] w-full p-1">
          <div className="w-full rounded-md bg-background sm:w-56">
            <div className="relative flex w-full items-center justify-start space-x-2 rounded-md p-2 text-left text-sm transition-all duration-75">
              <Icon name="user" className="mr-2 h-4 w-4" />
              <p className="text-sm">{name || user?.name?.toUpperCase() || user?.email}</p>
            </div>
            <Link
              className="relative flex w-full items-center justify-start space-x-2 rounded-md p-2 text-left text-sm transition-all duration-75 hover:bg-secondary"
              href="/profile"
              onClick={() => setOpenPopover(false)}
            >
              <Icon name="User" className="mr-2 h-4 w-4" />
              <p className="text-sm">My Profile</p>
            </Link>
            <button
              className="relative flex w-full items-center justify-start space-x-2 rounded-md p-2 text-left text-sm transition-all duration-75 hover:bg-secondary"
              onClick={() => {
                removeCookie('aceprepUserId');
                removeCookie('aceprepTenantId');
                setOpenPopover(false);
                signOut({
                  callbackUrl: '/',
                });
              }}
            >
              <Icon name="LogOut" className="mr-2 h-4 w-4" />
              <p className="text-sm">Sign out</p>
            </button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default Header;
