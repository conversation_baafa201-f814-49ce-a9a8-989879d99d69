import React from 'react';

import { Icon } from '@/icons';
import { AppLogo } from '@/layout/logo';

import { Button } from '@camped-ui/button';

import { UserDropDown } from '.';
import ModeToggle from '../mode-toggle';
import { MenuList } from './public';

const Header = ({ data, menuOpen, setMenuOpen, showMenus }) => {
  return (
    <header
      className="fixed w-screen border-b bg-background shadow-lg"
      style={
        menuOpen
          ? {
              zIndex: 1000,
              height: '100vh',
            }
          : { zIndex: 1000 }
      }
    >
      <div className="container mx-auto flex items-center justify-between px-6 py-2">
        {/* <Button variant='outline' size='icon' className='flex md:hidden'>
          <Icon name='menu' className='w-5 h-5' />
          </Button> */}
        <div className="flex py-2">
          <AppLogo />
          {showMenus ? (
            <div className="hidden items-center justify-center gap-x-2 md:flex">
              <MenuList setMenuOpen={() => {}} />
            </div>
          ) : null}
        </div>

        <div className="flex items-center justify-center gap-x-4">
          <UserDropDown
            user={{
              ...data?.user,
              id: data?.userId,
              updatedAt: data?.updatedAt,
            }}
            name={(data as any)?.name}
          />
          {/* <ModeToggle /> */}
        </div>
      </div>
      {showMenus && menuOpen ? (
        <div className="flex items-start pl-6">
          <MenuList setMenuOpen={() => {}} />
        </div>
      ) : null}
    </header>
  );
};

export default Header;
