'use client';

import React, { useState } from 'react';

import <PERSON> from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { AppLogo } from '@/layout/logo';

import { Button, buttonVariants } from '@camped-ui/button';
import { cn } from '@camped-ui/lib';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@camped-ui/navigation-menu';
import { Separator } from '@camped-ui/separator';
import { Sheet, SheetContent, SheetTrigger } from '@camped-ui/sheet';

import ModeToggle from '../mode-toggle';

const components: {
  title: string;
  href: string;
  description: string;
  iconName: string;
}[] = [
  {
    title: 'Mock Interview',
    href: '/mock/category',
    iconName: 'Briefcase',
    description: 'Practice and prepare for interviews through simulated interview sessions.',
  },
  {
    title: 'Tailored Practice',
    href: '/tailored-practice',
    iconName: 'Cog',
    description: 'Role-specific interview practice designed to enhance your skills and knowledge.',
  },
  {
    title: 'AI Resume Builder',
    href: '/resume',
    iconName: 'FileText',
    description:
      'Effortlessly create and customize professional resumes to stand out from the crowd.',
  },
  {
    title: 'Coding Challenges',
    href: '/coding-problems',
    iconName: 'Codesandbox',
    description:
      'Take on coding challenges to improve your coding skills and problem-solving abilities.',
  },
  {
    title: 'Frontend Practice',
    href: '/frontend-practice',
    iconName: 'TerminalSquare',
    description:
      'Sharpen your frontend development skills by working on various frontend projects.',
  },
];

const menuContentsAcePrep = [
  // {
  //   title: 'Pricing',
  //   href: '/pricing',
  //   iconName: 'Gem',
  // },
  {
    title: 'Blog',
    href: '/blog',
    iconName: 'Blocks',
  },
  {
    title: 'Contact Us',
    href: '/contact',
    iconName: 'Headset',
  },
];

const menuContentsHire = [
  {
    title: 'Blog',
    href: '/blog',
    iconName: 'Blocks',
  },
  {
    title: 'Contact Us',
    href: '/contact',
    iconName: 'Headset',
  },
];
const menuContents =
  process.env.NEXT_PUBLIC_PLATFORM === 'hire' ? menuContentsHire : menuContentsAcePrep;

const PublicHeader = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <>
      <header
        className="fixed hidden w-screen border-b bg-background shadow-lg md:flex"
        style={
          menuOpen
            ? {
                zIndex: 1000,
                height: '100vh',
              }
            : { zIndex: 1000 }
        }
      >
        <div className="container mx-auto flex items-center justify-between px-6 py-2">
          <div className="flex py-2">
            <AppLogo />
            <div className="hidden items-center justify-center gap-x-2 pl-2 md:flex">
              <MenuList setMenuOpen={setMenuOpen} />
            </div>
          </div>

          <div className="flex items-center justify-center gap-x-4">
            <a href="/sign-in">
              <Button>SIGN IN</Button>
            </a>
            <ModeToggle />
          </div>
        </div>
        {menuOpen ? (
          <div className="flex flex-col items-start pl-6">
            <MenuList setMenuOpen={setMenuOpen} />
          </div>
        ) : null}
      </header>
      <div className="flex md:hidden">
        <div className="md:hiddden border-custom-border-200 bg-custom-sidebar-background-100 z-10 flex w-full flex-shrink-0 flex-row items-center justify-between border-b px-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="px-2.5 py-2">
                <Icon name="Menu" className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-3/4 p-0">
              <div className="h-full">
                <div className="m-3">
                  <AppLogo />
                </div>
                <div className="mb-2 border-b" />
                <div className="mt-4 flex h-full flex-col gap-2 md:hidden">
                  <MobileMenuList setMenuOpen={setMenuOpen} />
                </div>
              </div>
            </SheetContent>
          </Sheet>
          <div className="container mx-auto flex items-center justify-between px-6 py-3">
            <div className="flex">
              <AppLogo />
            </div>

            <div className="flex items-center justify-center gap-x-4">
              <Link
                href="/sign-in"
                className="hover:[linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), #0D2247] group flex scale-100 items-center justify-center gap-x-2 rounded-md bg-primary px-4 py-2 text-[13px] font-semibold text-white no-underline transition-all duration-75 active:scale-95"
                style={{ alignSelf: 'baseline' }}
              >
                SIGN IN
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export const MenuList = ({ setMenuOpen }) => {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        {process.env.NEXT_PUBLIC_PLATFORM !== 'hire' && (
          <NavigationMenuItem>
            <NavigationMenuTrigger>
              <p className="text-sm">Solutions</p>
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul className="grid w-[400px] gap-3 bg-background p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {components.map((component) => (
                  <ListItem key={component.title} title={component.title} href={component.href}>
                    {component.description}
                  </ListItem>
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        )}
        {menuContents.map((component, index) => (
          <Link
            key={index}
            href={component?.href}
            className={cn(buttonVariants({ variant: 'ghost' }))}
            onClick={() => setMenuOpen(false)}
          >
            {component?.title}
          </Link>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export const MobileMenuList = ({ setMenuOpen }) => {
  const router = useRouter();

  const pathname = usePathname() || '';
  return (
    <div>
      <div className="space-y-2 px-8">
        {components.map((link, index) => {
          const isActive = pathname.split('/').pop() === link.href.split('/').pop();

          return (
            <div className="flex items-center" key={index}>
              <Icon name={link.iconName} className="mb-0.5 h-5 w-5" />
              <div
                key={index}
                onClick={() => router.push(`${process.env.NEXT_PUBLIC_API_BASE_URL}/${link.href}`)}
                className={`group flex w-full items-center justify-start gap-2 px-3 py-2 text-sm font-medium outline-none ${
                  isActive ? 'bg-secondary' : ''
                } `}
              >
                {link?.title}
              </div>
            </div>
          );
        })}
      </div>
      <Separator className="my-2 opacity-50" />
      <div className="absolute bottom-0 w-full">
        <Separator className="my-2 opacity-50" />
        <div className="px-8">
          {menuContents.map((component, index) => (
            <div className="flex items-center" key={index}>
              <Icon name={component.iconName} className="mb-1 h-5 w-5" />
              <Button
                key={index}
                variant="ghost"
                onClick={() =>
                  router.push(`${process.env.NEXT_PUBLIC_API_BASE_URL}/${component.href}`)
                }
                className={`group flex w-full items-center justify-start gap-2.5 px-3 py-2 text-sm font-medium outline-none`}
              >
                {component?.title}
              </Button>
            </div>
          ))}
        </div>

        <div className="border-t p-4">
          <Button className="w-full" onClick={() => router.push('/sign-in')}>
            <Icon name="User" className="h-5 w-5" />
            <span className="ml-2">SIGN IN</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

const ListItem = React.forwardRef<React.ElementRef<'a'>, React.ComponentPropsWithoutRef<'a'>>(
  ({ className, title, children, ...props }, ref) => {
    return (
      <li>
        <NavigationMenuLink asChild>
          <a
            ref={ref}
            className={cn(
              'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground',
              className,
            )}
            {...props}
          >
            <div className="text-sm font-medium leading-none">{title}</div>
            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</p>
          </a>
        </NavigationMenuLink>
      </li>
    );
  },
);
ListItem.displayName = 'ListItem';

export default PublicHeader;
