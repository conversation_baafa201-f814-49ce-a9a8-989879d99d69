'use client';

import React, { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { createDiscordWebhook, updateDiscordWebhook } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';

import Modal from './modal';

export const AddDiscordWebhookModal = ({
  showModal,
  item,
  setShowModal,
  update,
  setUpdate,
  handleSubmit,
  isLoading,
  setIsLoading,
}) => {
  const form = useForm({
    defaultValues: {
      name: update?.name,
      url: update?.url,
    },
  });

  useEffect(() => {
    form.setValue('name', update?.name);
    form.setValue('url', update?.url);
  }, [update]);

  return (
    <Modal
      showModal={['add', 'update']?.includes(showModal)}
      setShowModal={() => {
        setUpdate(null);
        setShowModal(null);
      }}
    >
      <div className="mx-5 overflow-scroll rounded-xl bg-background shadow-lg sm:w-3/4 sm:overflow-hidden md:w-2/5">
        <div className="w-full rounded-t-lg p-4">
          <p className="text-xl font-bold">{item?.heading}</p>
          <p className="mt-1 text-sm">{item?.subHeading}</p>
        </div>
        <div className="pl-6 pr-6 text-lg">
          <hr />
          <div className="overflow-auto py-4 pl-5">
            <div className="w-full">
              <Form {...(form as any)} className="h-full w-full">
                <form
                  onSubmit={form.handleSubmit(handleSubmit as any)}
                  className="grid w-full grid-cols-1 gap-4 px-1"
                >
                  <FormField
                    control={form.control as any}
                    name="name"
                    rules={{ required: 'Please enter name' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Webhook Name
                          </FormLabel>
                          <FormControl className="flex-[6]">
                            <Input placeholder="Webhook Name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={form.control as any}
                    name="url"
                    rules={{
                      required: 'Please add URL',
                    }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="url" className="flex-[1] text-base">
                            URL
                          </FormLabel>
                          <FormControl className="w-full flex-[6]">
                            <Input placeholder="Webhook URL" {...field} />
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <div className="flex flex-row-reverse gap-x-3 p-6 pr-4">
                    <Button disabled={isLoading === 'add'}>
                      {isLoading === 'add' ? (
                        <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      {update ? 'Update' : 'Add'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setUpdate(null);
                        setShowModal(null);
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
          <hr className="mt-5" />
        </div>
      </div>
    </Modal>
  );
};
