'use client';

import React, { useEffect, useState } from 'react';

import { Icon } from '@/icons';
import { useForm } from 'react-hook-form';

import { Button } from '@camped-ui/button';
import { Dialog, DialogContent } from '@camped-ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';
import { cn } from '@camped-ui/lib';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';

export const AddGroupModal = ({ showModal, setShowModal, handleSubmit, isLoading, department }) => {
  const [openDepartmentPopover, setOpenDepartmentPopover] = useState(false);
  const form = useForm({});

  return (
    <Dialog
      open={showModal}
      onOpenChange={() => {
        setShowModal(!showModal);
      }}
    >
      <DialogContent className="mx-2 overflow-scroll rounded-xl shadow-lg sm:w-3/4 sm:overflow-hidden md:w-2/5">
        <div className="flex w-full justify-center rounded-t-lg p-2">
          <p className="text-xl font-bold">Add Group</p>
        </div>
        <div className="text-lg">
          <div className="overflow-auto py-4 pl-5">
            <div className="w-full">
              <Form {...(form as any)} className="h-full w-full">
                <form
                  onSubmit={form.handleSubmit(handleSubmit as any)}
                  className="grid w-full grid-cols-1 gap-4 px-1"
                >
                  <FormField
                    control={form.control as any}
                    name="name"
                    rules={{ required: 'Please enter name' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Name
                          </FormLabel>
                          <FormControl className="flex-[6]">
                            <Input placeholder="Name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={form.control as any}
                    name="department"
                    rules={{ required: 'Please enter department' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Department
                          </FormLabel>
                          <FormControl className="flex-[6]">
                            <Popover
                              modal={true}
                              open={openDepartmentPopover}
                              onOpenChange={() => setOpenDepartmentPopover(!openDepartmentPopover)}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant={'outline'}
                                  className={cn(
                                    'h-full w-full justify-start text-left font-normal',
                                    !field?.value && 'text-muted-foreground',
                                  )}
                                  onClick={() => setOpenDepartmentPopover(!openDepartmentPopover)}
                                >
                                  <span className="w-full text-left capitalize">
                                    {field?.value?.name || 'Select Department'}
                                  </span>
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="p-0 text-left">
                                <div className="rounded-md bg-background">
                                  {department?.department?.length === 0 ? (
                                    <span className="text-destructive">
                                      Please add a department
                                    </span>
                                  ) : (
                                    department?.department?.map((item, key) => (
                                      <Button
                                        key={key}
                                        variant="ghost"
                                        className="m-0 flex w-full items-center justify-start space-x-1 rounded-md text-left text-sm capitalize transition-all duration-75"
                                        onClick={() => {
                                          field.onChange(item);
                                          setOpenDepartmentPopover(false);
                                        }}
                                      >
                                        {item?.name}
                                      </Button>
                                    ))
                                  )}
                                </div>
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={form.control as any}
                    name="description"
                    rules={{}}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="url" className="flex-[1] text-base">
                            Description
                          </FormLabel>
                          <FormControl className="w-full flex-[6]">
                            <textarea
                              className="h-30 w-full rounded border bg-inherit p-2 text-sm focus:outline-2 focus:outline-offset-4 focus:outline-primary"
                              rows={4}
                              placeholder="Description"
                              {...field}
                            />
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <div className="flex flex-row-reverse gap-x-3 pr-4">
                    <Button disabled={isLoading === 'add'}>
                      {isLoading === 'add' ? (
                        <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      {'Add'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowModal(null);
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
