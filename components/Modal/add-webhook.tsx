'use client';

import React, { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useTestEndpoint } from '@/hooks/mutation/use-test-endpoint';
import { Icon } from '@/icons';
import { createWebhook, updateWebhook } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';

import Modal from './modal';

export const AddWebhookModal = ({ showModal, item, setShowModal, update, setUpdate }) => {
  const tenantId = getCookie('aceprepTenantId');
  const router = useRouter();
  const [isLoading, setIsLoading]: any = useState(null);
  const [testURL, setTestURL] = useState(false);
  const form = useForm({
    defaultValues: {
      name: update?.name,
      url: update?.url,
    },
  });
  useEffect(() => {
    form.setValue('name', update?.name);
    form.setValue('url', update?.url);
  }, [update]);
  const { mutate: handleTestEndpoint } = useTestEndpoint({ setTestURL, setIsLoading });
  const handleSubmit = async ({ url, name }) => {
    setIsLoading('add');
    await handleTestEndpoint({ url });
    if (!testURL) return;
    let webhook;
    if (update) {
      webhook = await updateWebhook({
        id: update?.id,
        data: { url, name, organizationId: tenantId },
      });
    } else {
      webhook = await createWebhook({ data: { url, name, organizationId: tenantId } });
    }
    if (webhook?.webhook?.id) {
      toast.success(`Webhook ${update ? 'updated' : 'added'} successfully`);
      setUpdate(null);
      setShowModal(null);
      router.refresh();
    } else {
      toast.error(`Failed to ${update ? 'update' : 'add'}  webhook`);
    }
    setIsLoading(null);
    return webhook;
  };

  return (
    <Modal
      showModal={['add', 'update']?.includes(showModal)}
      setShowModal={() => {
        setUpdate(null);
        setShowModal(null);
      }}
    >
      <div className="mx-5 overflow-scroll rounded-xl bg-background shadow-lg sm:w-3/4 sm:overflow-hidden md:w-2/5">
        <div className="w-full rounded-t-lg p-4">
          <p className="text-xl font-bold">{item?.heading}</p>
          <p className="mt-1 text-sm">{item?.subHeading}</p>
        </div>
        <div className="pl-6 pr-6 text-lg">
          <hr />
          <div className="overflow-auto py-4 pl-5">
            <div className="w-full">
              <Form {...(form as any)} className="h-full w-full">
                <form
                  onSubmit={form.handleSubmit(handleSubmit as any)}
                  className="grid w-full grid-cols-1 gap-4 px-1"
                >
                  <FormField
                    control={form.control as any}
                    name="name"
                    rules={{ required: 'Please enter name' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Webhook Name
                          </FormLabel>
                          <FormControl className="flex-[6]">
                            <Input placeholder="Webhook Name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <FormField
                    control={form.control as any}
                    name="url"
                    rules={{
                      required: 'Please add URL',
                    }}
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                          <FormLabel htmlFor="url" className="flex-[1] text-base">
                            URL
                          </FormLabel>
                          <div className="flex w-full gap-2">
                            <FormControl className="w-full flex-[6]">
                              <Input placeholder="Webhook URL" {...field} />
                            </FormControl>
                            <Button
                              size="sm"
                              disabled={
                                form.getValues('url')?.length === 0 ||
                                !Boolean(form.getValues('url')) ||
                                isLoading === 'test'
                              }
                              onClick={() => {
                                setIsLoading('test');
                                const url = form.getValues('url');
                                handleTestEndpoint({ url });
                              }}
                            >
                              {isLoading === 'test' ? (
                                <Icon
                                  name="Loader2"
                                  className="mr-1 h-5 w-5 animate-spin text-white"
                                />
                              ) : (
                                <></>
                              )}
                              Test Endpoint
                            </Button>
                          </div>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                  <div className="flex flex-row-reverse gap-x-3 p-6 pr-4">
                    <Button disabled={isLoading === 'add'}>
                      {isLoading === 'add' ? (
                        <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      {update ? 'Update' : 'Add'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setUpdate(null);
                        setShowModal(null);
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>
          <hr className="mt-5" />
        </div>
      </div>
    </Modal>
  );
};
