'use client';

import React, { useState } from 'react';

import { useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { updateIntegration } from '@/services/apicall';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Label } from '@camped-ui/label';
import { RadioGroup, RadioGroupItem } from '@camped-ui/radio-group';

import Modal from './modal';

export const ChannelSelectModal = ({ channels, item, integration }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(integration?.page);
  const router = useRouter();
  const handleSubmit = async (id, channel) => {
    setIsLoading(true);
    const updateChannel = await updateIntegration({ id, data: channel });
    setIsLoading(false);

    if (updateChannel?.integration?.id) {
      toast.success('Channel added successfully');
      router.back();
    } else {
      toast.error('Failed to add channel');
    }
    return updateChannel;
  };
  return (
    <Modal showModal={true} setShowModal={() => router.back()}>
      <div className="mx-5 overflow-scroll rounded-xl bg-background shadow-lg sm:w-3/4 sm:overflow-hidden md:w-2/5">
        <div className="w-full rounded-t-lg p-4">
          <p className="text-xl font-bold">{item?.heading}</p>
          <p className="mt-1 text-sm">{item?.subHeading}</p>
        </div>
        <div className="pl-6 pr-6 text-lg">
          <hr />
          <div className="mb-2 h-[150px] overflow-auto pl-5 pt-4">
            <div className="w-full">
              <RadioGroup>
                {channels?.length > 0 ? (
                  channels?.map((item, index) => (
                    <div className="flex items-center space-x-2" key={index}>
                      <RadioGroupItem
                        value={`option-${index}`}
                        id={`option-${index}`}
                        onClick={() => setSelectedChannel(item)}
                        checked={selectedChannel?.name === item?.name}
                      />
                      <Label htmlFor={`option-${index}`} className="px-2 text-base font-medium">
                        {item?.name}
                      </Label>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="mx-auto flex max-w-[600px] flex-col items-center justify-center text-center">
                      <br />
                      <p className="mt-2 text-sm text-muted-foreground">{`No channel found!`}</p>
                      <br />
                    </div>
                  </>
                )}
              </RadioGroup>
            </div>
          </div>
          <hr className="mt-5" />
        </div>
        <div className="flex flex-row-reverse gap-x-3 p-6 pr-4">
          <Button
            disabled={isLoading}
            onClick={() => handleSubmit(integration?.id, { page: selectedChannel })}
          >
            {isLoading ? <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" /> : null}
            Add
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              router.back();
            }}
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};
