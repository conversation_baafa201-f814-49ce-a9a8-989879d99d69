'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { createApiKey } from '@/services/apicall';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@camped-ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';

export default function CreateApiKey({ tenantId, userId }) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const form = useForm();

  const handleCreate = async (data) => {
    toast.loading('Creating API key...');
    setIsLoading(true);
    const apiKey = await createApiKey({ ...data, organizationId: tenantId, createdById: userId });
    setIsLoading(false);
    toast.remove();
    if (apiKey?.api?.id) {
      toast.success('API key created successfully!');
      router.refresh();
      window.location.href = '/tenant/api-keys';
    } else {
      toast.error('Failed to create API key');
    }
  };

  return (
    <Dialog open={true} onOpenChange={() => router.back()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add API Key</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <div className="flex">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleCreate)} className="w-full space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex">
                          <FormLabel htmlFor="url">Name*</FormLabel>
                        </div>
                        <FormControl className="w-full">
                          <Input placeholder="Your API key name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                    rules={{ required: 'API key name is required' }}
                  />
                  <DialogFooter>
                    <Button type="submit" disabled={isLoading}>
                      Add
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        router.back();
                      }}
                      variant="outline"
                    >
                      Cancel
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
