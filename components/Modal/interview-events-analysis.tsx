'use client';

import { useRouter } from 'next/navigation';

import ProgressBar from '@/components/ui/progress-bar';

import { Card, CardDescription, CardTitle } from '@camped-ui/card';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@camped-ui/sheet';

export const InterviewAnalysis = ({ eventDetails }) => {
  const router = useRouter();

  const statusCounts = eventDetails?.careerPractices?.reduce((counts, interview) => {
    counts[interview.interviewStatus] = (counts[interview.interviewStatus] || 0) + 1;
    return counts;
  }, {});

  // Calculate the percentage of each interviewStatus
  const totalCount = eventDetails?.careerPractices?.length;
  const percentages = {};
  for (const status in statusCounts) {
    percentages[status] = ((statusCounts[status] / totalCount) * 100).toFixed(2);
  }

  return (
    <Sheet open={true} onOpenChange={() => router.back()}>
      <SheetContent
        className="h-full overflow-scroll rounded-l-2xl"
        style={{ maxWidth: '80vw', width: '50%' }}
      >
        <SheetHeader className="px-3">
          <SheetTitle className="px-1">Analytics</SheetTitle>
        </SheetHeader>
        <Card className="mt-4 p-4">
          <CardTitle className="text-xl">Total Candidates Status</CardTitle>
          <CardTitle className="mb-3 mt-1">{totalCount}</CardTitle>
          <div className="mt-3 w-full space-y-3 md:mt-0">
            {['Not Started', 'Partially Completed', 'Completed']?.map((item) => (
              <>
                <div key={item} className="mt-2 flex items-center justify-between gap-1">
                  <CardDescription className="capitalize">
                    {item.replace(/_/g, ' ')}
                  </CardDescription>
                  <CardDescription>{`${percentages[item] ?? 0}%`}</CardDescription>
                </div>
                <ProgressBar finalProgress={percentages[item]} height="h-2" />
              </>
            ))}
          </div>
        </Card>
      </SheetContent>
    </Sheet>
  );
};
