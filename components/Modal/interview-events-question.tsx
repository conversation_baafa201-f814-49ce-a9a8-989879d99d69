'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { capitalizeFirstLetter } from '@/utils/string.helper';
import ReactMarkdown from 'react-markdown';

import { Badge } from '@camped-ui/badge';
import { Card } from '@camped-ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@camped-ui/collapsible';
import { <PERSON><PERSON>, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from '@camped-ui/sheet';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@camped-ui/table';

export const Questions = ({ eventDetails, show, setShow, screen }) => {
  const [open, setOpen]: any = useState(0);
  const [showDescription, setShowDescription]: any = useState(null);
  const router = useRouter();
  const sectionTopic = {
    'general-aptitude': 'Aptitude',
    'logical-reasoning': 'Logical Reasoning',
    'verbal-ability': 'Verbal Ability',
    'current-affairs-and-gk': 'Current Affairs and GK',
    'video-interview': 'Video Question',
    'coding-interview': 'Coding Question',
    'frontend-interview': 'Frontend Question',
    'multiple-choice': 'MCQs',
  };
  const groupedData = eventDetails?.questions?.reduce((acc, item) => {
    if (!acc[item.round]) {
      acc[item.round] = [];
    }
    acc[item.round].push(item);
    return acc;
  }, {});
  const isInterviewathon = screen === 'interviewathon';

  return (
    <Sheet open={show} onOpenChange={setShow ? setShow : () => router.back()}>
      <SheetContent
        className="h-full overflow-scroll rounded-l-2xl"
        style={{ maxWidth: '80vw', width: '50%' }}
      >
        <SheetHeader className="">
          <SheetTitle className="text-2xl">
            {isInterviewathon ? 'Interviewathon' : 'Interview'} Details
          </SheetTitle>
        </SheetHeader>

        <div className="mb-4 mt-2 flex w-full flex-col">
          {eventDetails?.role && (
            <h1 className="text-left text-base">
              <span className="text-lg font-bold">Role:</span> {eventDetails?.role}
            </h1>
          )}
          {eventDetails?.level && (
            <h1 className="text-left text-base">
              <span className="text-lg font-bold">Level:</span> {eventDetails?.level}
            </h1>
          )}
          {eventDetails?.jobDescription && (
            <h1 className="text-left text-base">
              <span className="text-lg font-bold">Job Description: </span>
              <div className="prose prose-sm" style={{ overflowY: 'auto' }}>
                <ReactMarkdown className="markDown m-4">
                  {eventDetails?.jobDescription?.replace(/\\n/g, '\n')}
                </ReactMarkdown>
              </div>
            </h1>
          )}
        </div>
        {eventDetails?.isAiQuestion ? (
          <div>
            <SheetTitle>This is an AI Interview</SheetTitle>
            <p className="text-left">
              <span className="font-semibold">Number of questions:</span>{' '}
              {eventDetails?.aiQuestionCount}
            </p>

            <p className="mt-4 opacity-65">
              The questions will vary based on the candidate&apos;s responses to questions.
            </p>
          </div>
        ) : (
          <>
            {Object.entries(groupedData ?? {})?.map(([key, value]: any, index) => (
              <Collapsible
                key={index}
                open={index === open}
                onOpenChange={() => {
                  setOpen(index === open ? null : index);
                }}
              >
                <CollapsibleTrigger className="w-full">
                  <div
                    className={`flex border ${
                      index !== open ? 'border-t-0' : 'rounded-b-none border-b-0'
                    } mb-3 w-full justify-between rounded-md p-4`}
                  >
                    <h2 className="text-left text-xl font-semibold">
                      {sectionTopic[key] ?? capitalizeFirstLetter(key)}
                    </h2>
                    <Icon name={index === open ? 'ChevronUp' : 'ChevronDown'} className="h-6 w-6" />
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  {key === 'multiple-choice' ? (
                    <div>
                      {value?.map((question, index) => (
                        <div key={index} className="py-4">
                          <div className="flex items-start justify-between gap-2">
                            <p>
                              {index + 1}. {question?.question}{' '}
                              <Badge variant="outline">
                                {question?.categoryName ??
                                  sectionTopic[question?.category] ??
                                  capitalizeFirstLetter(question?.category?.replaceAll(/-/g, ' '))}
                              </Badge>
                            </p>
                          </div>
                          <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-1">
                            {question?.options?.map((option, optionIndex) => {
                              const isSourceCode = question?.correctAnswer === option;
                              return (
                                <Card
                                  key={optionIndex}
                                  className={`w-full p-4 ${isSourceCode ? 'bg-secondary' : ''} `}
                                >
                                  <p className="flex items-center gap-1 truncate">{option}</p>
                                </Card>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[100px]">S.No</TableHead>
                          {value?.[0]?.category && (
                            <TableHead className="w-[100px]">Category</TableHead>
                          )}
                          <TableHead>Question</TableHead>
                          {['coding-interview', 'frontend-interview']?.includes(key) && (
                            <TableHead>Description</TableHead>
                          )}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {value?.map((item, index) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">{index + 1}</TableCell>
                            {value?.[0]?.category && (
                              <TableCell>{sectionTopic[item?.category]}</TableCell>
                            )}
                            <TableCell>{item?.question}</TableCell>
                            {['coding-interview', 'frontend-interview']?.includes(key) && (
                              <TableCell>
                                <p
                                  className={showDescription === index ? '' : 'line-clamp-3'}
                                  onClick={() =>
                                    setShowDescription(showDescription === index ? null : index)
                                  }
                                >
                                  {item?.questionDescription}
                                </p>
                              </TableCell>
                            )}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CollapsibleContent>
              </Collapsible>
            ))}
          </>
        )}
      </SheetContent>
    </Sheet>
  );
};
