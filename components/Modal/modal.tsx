'use client';

import { Dispatch, SetStateAction, useCallback, useEffect, useRef } from 'react';

import { motion } from 'framer-motion';

export default function Modal({
  children,
  showModal,
  setShowModal,
}: {
  children: React.ReactNode;
  showModal: boolean;
  setShowModal: Dispatch<SetStateAction<boolean>>;
}) {
  const desktopModalRef = useRef(null);

  const onKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowModal(false);
      }
    },
    [setShowModal],
  );

  useEffect(() => {
    document.addEventListener('keydown', onKeyDown);
    return () => document.removeEventListener('keydown', onKeyDown);
  }, [onKeyDown]);

  return (
    <>
      {showModal && (
        <>
          {/* Add a focusable element within the modal */}
          <motion.div
            ref={desktopModalRef}
            key="desktop-modal"
            className="fixed inset-0 z-40 flex min-h-screen items-center justify-center"
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.95 }}
            onMouseDown={(e) => {
              if (desktopModalRef.current === e.target) {
                setShowModal(false);
              }
            }}
            style={{ zIndex: 200 }}
          >
            {/* Add a focusable element here */}
            {children}
          </motion.div>
          <motion.div
            key="desktop-backdrop"
            className="z-100 fixed inset-0 bg-gray-100 bg-opacity-10 backdrop-blur"
            style={{ zIndex: 100 }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowModal(false)}
          />
        </>
      )}
    </>
  );
}
