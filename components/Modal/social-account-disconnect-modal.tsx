import { useDisconnectSocialConnect } from '@/hooks/mutation/use-disconnect';
import { Icon } from '@/icons';
import { useSocialAccountStore } from '@/packages/shared-store';

import { Button } from '@camped-ui/button';

import Modal from './modal';

export const DisconnectAccountModal = () => {
  const { mutate: handleDisconnect, isLoading } = useDisconnectSocialConnect();
  const data = useSocialAccountStore((state) => (state as any).disconnectData);
  const updateData = useSocialAccountStore((state) => (state as any).disconnect);

  if (!data?.showModal) {
    return null;
  }

  return (
    <Modal showModal={data?.showModal} setShowModal={() => updateData(false)}>
      <div className="mx-5 overflow-scroll rounded-lg bg-background p-4 shadow-lg sm:w-3/4 sm:overflow-hidden md:w-1/3">
        <p className="px-2 py-2">
          {`Are you sure you want to disconnect your ${data?.platform} account?`}
        </p>
        <p className="px-2 pb-4 text-sm opacity-70">
          {`Disconnecting your account will revoke access to the ${data?.platform} authentication.`}
        </p>
        <div className="flex flex-1 items-center justify-end gap-2">
          <Button variant="outline" onClick={() => updateData(false)}>
            Cancel
          </Button>
          <Button
            disabled={isLoading}
            variant="default"
            onClick={() =>
              handleDisconnect({ id: data?.id, platform: data?.platform?.toUpperCase() })
            }
          >
            {isLoading ? <Icon name="Loader2" className={`mr-2 h-5 w-5 animate-spin`} /> : null}
            Yes
          </Button>
        </div>
      </div>
    </Modal>
  );
};
