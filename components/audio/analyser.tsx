import { useEffect, useRef, useState } from 'react';

import { Button } from '@camped-ui/button';

import WaveForm from './waveform';

export default function AudioAnalyzer(props) {
  const { permissionStatus, setPermissionStatus, soundDetected, setSoundDetected } = props;
  const [analyzerData, setAnalyzerData] = useState(null) as any;
  const audioElmRef = useRef(null) as any;
  const isListening = permissionStatus?.status === 'Listening ...';

  const audioAnalyzer = (stream) => {
    const audioCtx = new (window.AudioContext || window.AudioContext)();
    const analyzer = (audioCtx as any).createAnalyser();
    analyzer.fftSize = 2048;

    const bufferLength = analyzer.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const source = audioCtx.createMediaStreamSource(stream);
    source.connect(analyzer);

    setAnalyzerData({ analyzer, bufferLength, dataArray });

    let soundDetected = false;

    const checkForSound = () => {
      analyzer.getByteTimeDomainData(dataArray);

      // Check if there is a significant amplitude in the data array
      const isSound = dataArray.some((value) => Math.abs(value - 128) > 10); // Adjust threshold as needed

      if (isSound && !soundDetected) {
        setSoundDetected(true); // Set state when sound is detected
      } else if (!isSound && soundDetected) {
        setSoundDetected(false); // Reset state when no sound
      }

      requestAnimationFrame(checkForSound); // Continue monitoring
    };

    requestAnimationFrame(checkForSound);
  };
  const testAudioAnalysis = async (streamAudio = false) => {
    try {
      setPermissionStatus({
        status: !streamAudio ? 'success' : 'loading',
        message: !streamAudio ? 'Audio has access permission' : 'Listening ...',
      });
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioElmRef.current.srcObject = stream;
      if (streamAudio) audioAnalyzer(stream);
    } catch (error) {
      setPermissionStatus({
        status: 'error',
        message:
          error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError'
            ? 'Permission denied !'
            : 'Please check your mic.',
      });
    }
  };

  useEffect(() => {
    testAudioAnalysis(true);
  }, []);

  return (
    <div className="relative flex h-full w-full flex-col items-center justify-center gap-1">
      {permissionStatus?.status === 'error' ? (
        <p className="max-w-3xl px-4 text-center font-medium">
          Please allow access to the microphone in your browser settings.
        </p>
      ) : analyzerData ? (
        <div className="h-24 w-full rounded-md p-4">
          <WaveForm analyzerData={analyzerData} />
        </div>
      ) : (
        <div className="h-24 w-full p-4">
          <div
            className="flex h-full w-full cursor-pointer items-center justify-center rounded-md border bg-background"
            onClick={() => testAudioAnalysis(true)}
          >
            <p>Test your Audio</p>
          </div>
        </div>
      )}

      {permissionStatus?.status === 'error' ? (
        <Button
          onClick={() => {
            window.location.reload();
          }}
          className="mt-2"
        >
          Reload
        </Button>
      ) : null}

      <audio ref={audioElmRef} style={{ display: 'none' }} />
    </div>
  );
}
