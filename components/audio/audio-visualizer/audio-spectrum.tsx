import React, { useRef } from 'react';

import { useAnimationFrame } from '@/hooks/client/useAnimationFrame';
import { drawSpectrum } from '@/utils/spectrumDrawing';

interface AudioSpectrumProps {
  frequencyData: Uint8Array | null;
  size: number;
}

export const AudioSpectrum: React.FC<AudioSpectrumProps> = ({ frequencyData, size }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useAnimationFrame(() => {
    const canvas = canvasRef.current;
    if (!canvas || !frequencyData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    drawSpectrum(ctx, size, frequencyData);
  });

  return (
    <canvas
      ref={canvasRef}
      width={size}
      height={size}
      className="pointer-events-none absolute transform-gpu"
    />
  );
};
