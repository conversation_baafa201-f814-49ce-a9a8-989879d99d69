import React from 'react';

import { useAudioAnalyzer } from '@/hooks/client/use-audio-analyzer';
import { Icon } from '@/icons';
import { User } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';

import { AudioSpectrum } from './audio-spectrum';

interface AudioVisualizerProps {
  audioStream: MediaStream | null;
  size?: number;
  userId?: String;
}

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({ audioStream, size = 280, userId }) => {
  const frequencyData = useAudioAnalyzer(audioStream);

  return (
    <div className="relative flex items-center justify-center">
      {audioStream && <AudioSpectrum frequencyData={frequencyData} size={size} />}
      <div
        className="absolute rounded-full bg-gray-800/50 backdrop-blur-sm"
        style={{
          width: size * 0.35,
          height: size * 0.35,
        }}
      >
        <Avatar className="h-full w-full">
          <AvatarImage
            src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${userId}`}
            alt="@camped"
          />
          <AvatarFallback>
            <div className="flex h-24 w-24 rounded-full bg-gray-300 p-5 sm:h-32 sm:w-32">
              <User className="h-full w-full bg-none" />
            </div>
          </AvatarFallback>
        </Avatar>
      </div>
    </div>
  );
};

export default AudioVisualizer;
