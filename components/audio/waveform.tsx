import { useEffect, useRef } from 'react';

import useSize from './useSize';

function animateBars(analyser, canvas, canvasCtx, dataArray, bufferLength) {
  analyser.getByteFrequencyData(dataArray);

  canvasCtx.fillStyle = '#000';

  const HEIGHT = canvas.height / 2;

  const barWidth = 8;
  const spacing = 2;
  const radius = 3;

  let x = 0;

  for (let i = 0; i < bufferLength; i++) {
    const barHeight = (dataArray[i] / 255) * HEIGHT;
    const blueShade = Math.floor((dataArray[i] / 255) * 5);
    const blueHex = ['#61dafb', '#5ac8fa', '#50b6f5', '#419de6', '#1443E3'][blueShade];
    canvasCtx.fillStyle = blueHex;

    // Draw rounded rectangle
    canvasCtx.beginPath();
    canvasCtx.moveTo(x + radius, HEIGHT);
    canvasCtx.arcTo(x + barWidth, HEIGHT, x + barWidth, HEIGHT - barHeight, radius);
    canvasCtx.arcTo(x + barWidth, HEIGHT - barHeight, x, HEIGHT - barHeight, radius);
    canvasCtx.arcTo(x, HEIGHT - barHeight, x, HEIGHT, radius);
    canvasCtx.arcTo(x, HEIGHT, x + barWidth, HEIGHT, radius);
    canvasCtx.closePath();
    canvasCtx.fill();

    x += barWidth + spacing;
  }
}

const WaveForm = ({ analyzerData }) => {
  const canvasRef = useRef(null);
  const { dataArray, analyzer, bufferLength } = analyzerData;
  const [width, height] = useSize();

  const draw = (dataArray, analyzer, bufferLength) => {
    const canvas = canvasRef.current as any;
    if (!canvas || !analyzer) return;
    const canvasCtx = canvas.getContext('2d');

    const animate = () => {
      requestAnimationFrame(animate);
      // eslint-disable-next-line no-self-assign
      canvas.width = canvas.parentElement.clientWidth;
      canvasCtx.translate(-50, canvas.offsetHeight / -113);
      animateBars(analyzer, canvas, canvasCtx, dataArray, bufferLength);
    };

    animate();
  };

  useEffect(() => {
    draw(dataArray, analyzer, bufferLength);
  }, [dataArray, analyzer, bufferLength]);

  return (
    <div
      className="relative w-full overflow-hidden rounded-md border bg-background px-1 py-4"
      style={{ width: '100%', height: '100%', overflow: 'hidden' }}
    >
      <canvas ref={canvasRef} width={'100%'} height={'100%'} style={{ position: 'absolute' }} />
    </div>
  );
};

export default WaveForm;
