import React from 'react';

import { getFormattedDate } from '@/utils/formateDate';
import { storyblokEditable } from '@storyblok/react';

interface blokProps {
  blok: any | undefined | null;
}

export const Author = ({ blok }: blokProps) => {
  const { title, category, publishedDate, readTime } = blok || '';
  const { linkedInUrl, profileImage, name, designation } = blok?.author?.content || '';

  return (
    <div {...storyblokEditable(blok)}>
      <div className="mx-auto mb-2 mt-0 flex flex-col justify-between text-sm sm:flex-row sm:items-center">
        <p>
          <svg
            viewBox="0 0 24 24"
            className="-mt-0.5 inline-block h-4 w-4 dark:text-gray-400"
            astro-icon="tabler:clock"
          >
            <g
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="icon-tabler"
            >
              <circle cx="12" cy="12" r="9"></circle>
              <path d="M12 7v5l3 3"></path>
            </g>
          </svg>{' '}
          <time dateTime={String(publishedDate)}>{getFormattedDate(publishedDate)}</time> ●{' '}
          <a
            className="capitalize underline hover:text-primary"
            href={`/category/${category?.toLowerCase()?.replaceAll(' ', '-')}/`}
          >
            {category}
          </a>{' '}
          ● {readTime} min{Number(readTime) > 1 && 's'} read
        </p>
      </div>

      <h1 className="leading-tighter font-heading mx-auto text-4xl font-bold tracking-tighter md:text-5xl">
        {title}
      </h1>
      <div className="mx-auto pt-4">
        <div className="border-t dark:border-slate-700"></div>
      </div>
      <div className=""></div>
    </div>
  );
};
