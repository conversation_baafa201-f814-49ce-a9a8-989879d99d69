export const ShareIcon = (blok) => {
  const { title, pathname } = blok;
  const baseURL =
    process.env.NEXT_PUBLIC_PLATFORM === 'hire'
      ? 'https://hire.flinkk.io'
      : 'https://aceprep.camped.academy';
  const url = `${baseURL}${pathname}`;

  return (
    <div className="mt-5 align-middle text-gray-500 sm:mt-1 dark:text-slate-600">
      <span className="align-super font-bold text-gray-400 dark:text-slate-400">Share:</span>
      <button
        className="ml-2"
        title="Twitter Share"
        data-aw-social-share="twitter"
        data-aw-url={url}
        data-aw-text={title}
      >
        {/* Twitter icon */}
      </button>
      <button
        className="ml-2"
        title="Facebook Share"
        data-aw-social-share="facebook"
        data-aw-url={url}
      >
        {/* Facebook icon */}
      </button>
      <button
        className="ml-2"
        title="Linkedin Share"
        data-aw-social-share="linkedin"
        data-aw-url={url}
        data-aw-text={title}
      >
        {/* LinkedIn icon */}
      </button>
      <button
        className="ml-2"
        title="Whatsapp Share"
        data-aw-social-share="whatsapp"
        data-aw-url={url}
        data-aw-text={title}
      >
        {/* Whatsapp icon */}
      </button>
      <button
        className="ml-2"
        title="Email Share"
        data-aw-social-share="mail"
        data-aw-url={url}
        data-aw-text={title}
      >
        {/* Email icon */}
      </button>
    </div>
  );
};
