import { useTheme } from 'next-themes';

export const Tags = ({ name }) => {
  const { theme } = useTheme();
  return (
    <li
      className={`mb-2 mr-2 inline-block rounded-md px-2 py-0.5 font-medium lowercase ${
        theme === 'light' ? 'bg-black' : 'bg-[#EEEEF2]'
      }`}
    >
      <a
        className="text-muted hover:text-primary"
        href={`/tag/${name?.toLowerCase()?.replaceAll(' ', '-')}/`}
      >
        {name}
      </a>
    </li>
  );
};
