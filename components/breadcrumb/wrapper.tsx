'use client';

import { useRouter } from 'next/navigation';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@camped-ui/breadcrumb';
import { cn } from '@camped-ui/lib';

export const BreadCrumbWrapper = ({ data }) => {
  const router = useRouter();

  const handleClick = (key) => {
    if (key === 0) {
      router.back();
    }
  };

  return (
    <Breadcrumb className="mb-2 justify-start gap-1">
      {data?.map((item, index) => (
        <>
          <BreadcrumbItem
            index={index}
            className={cn('h-7 px-2', index === 0 && 'cursor-pointer')}
            onClick={() => handleClick(index)}
          >
            <BreadcrumbLink>{item?.title}</BreadcrumbLink>
          </BreadcrumbItem>
          {data?.length !== index + 1 ? <BreadcrumbSeparator /> : null}
        </>
      ))}
    </Breadcrumb>
  );
};
