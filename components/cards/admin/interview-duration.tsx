import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';

export const InterviewDurationCard = ({ startTime, completedTime, title }) => {
  const details = [
    {
      title: 'Start Time',
      value: startTime
        ? `${formatTimestamp(startTime)?.date} ${formatTimestamp(startTime)?.time}`
        : 'Not Started yet',
    },
    {
      title: 'Completed Time',
      value: completedTime
        ? `${formatTimestamp(completedTime)?.date}  ${formatTimestamp(completedTime)?.time}`
        : 'Not Completed yet',
    },
  ];
  return (
    <Card>
      <CardHeader className="p-3">
        <CardTitle className="text-xl">{title}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="flex items-center justify-evenly gap-6 p-4 text-center">
        {details?.map((item, index) => (
          <div key={index} className="flex flex-col items-center justify-center">
            <CardDescription className="text-xl font-bold">{item?.value}</CardDescription>
            <CardDescription>{item?.title}</CardDescription>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

const formatTimestamp = (
  timestamp: string,
  includeSeconds: boolean = false,
): {
  date: string;
  time: string;
} => {
  const date = new Date(timestamp);

  const optionsDate: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };

  let optionsTime: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: 'numeric',
  };

  if (includeSeconds) {
    optionsTime = { ...optionsTime, second: 'numeric' };
  }

  const formattedDate = date.toLocaleDateString('en-US', optionsDate);
  const formattedTime = date.toLocaleTimeString('en-US', optionsTime);

  return {
    date: formattedDate,
    time: formattedTime,
  };
};
