import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';

export const InterviewProctoringCard = ({ tabSwitch, fullScreen, title }) => {
  const details = [
    {
      title: 'Tab Switch',
      value: tabSwitch ?? '-',
    },
    {
      title: 'Exit Full Screen',
      value: fullScreen ?? '-',
    },
  ];
  return (
    <Card>
      <CardHeader className="p-3">
        <CardTitle className="text-xl">{title}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="flex items-center justify-evenly gap-6 p-4 text-center">
        {details?.map((item, index) => (
          <div key={index} className="flex flex-col items-center justify-center">
            <CardDescription className="text-xl font-bold">{item?.value}</CardDescription>
            <CardDescription>{item?.title}</CardDescription>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
