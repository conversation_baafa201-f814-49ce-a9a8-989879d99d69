import { formatTimestamp } from '@/components/data-table/interview-candidate/columns';
import { calculateDuration } from '@/utils/date-time.helper';

import { Card } from '@camped-ui/card';
import { Table } from '@camped-ui/table';
import { TableBody, TableCell, TableRow } from '@camped-ui/table';

export const InterviewQuestionTiming = ({ questions, activeIndex }) => {
  const details = [
    {
      label: 'Start Time',
      value: questions?.[activeIndex]?.startTime
        ? `${formatTimestamp(questions?.[activeIndex]?.startTime)?.date} ${formatTimestamp(
            questions?.[activeIndex]?.startTime,
            true,
          )?.time}`
        : questions?.[activeIndex - 1]?.completedTime
        ? `${formatTimestamp(questions?.[activeIndex - 1]?.completedTime)?.date} ${formatTimestamp(
            questions?.[activeIndex - 1]?.completedTime,
            true,
          )?.time}`
        : 'Not Started yet',
    },
    {
      label: 'Completed Time',
      value: questions?.[activeIndex]?.completedTime
        ? `${formatTimestamp(questions?.[activeIndex]?.completedTime)?.date} ${formatTimestamp(
            questions?.[activeIndex]?.completedTime,
            true,
          )?.time}`
        : 'Not Completed yet',
    },
    {
      label: 'Duration spent',
      value:
        questions?.[activeIndex]?.startTime || questions?.[activeIndex - 1]?.completedTime
          ? `${calculateDuration({
              startTime: new Date(
                questions?.[activeIndex]?.startTime
                  ? questions?.[activeIndex]?.startTime
                  : questions?.[activeIndex - 1]?.completedTime,
              ),
              endTime: new Date(questions?.[activeIndex]?.completedTime),
            })}`
          : '-',
    },
  ];
  return (
    <Card>
      <Table>
        <TableBody>
          {details.map((data, index) => {
            return (
              <TableRow key={index}>
                <TableCell className="border-r font-semibold">{data?.label}</TableCell>
                <TableCell>{data?.value}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </Card>
  );
};
