import Image from 'next/image';

import { Tags } from '@/components/blog/Tags';

import { getFormattedDate } from '../../utils/formateDate';

export const BlogCard = ({ post }) => {
  const { blogDetails } = post.content;
  const { category, publishedDate, title, bannerImage, description, tags, author } =
    blogDetails?.[0];

  return (
    <article className={`mx-auto grid max-w-md gap-6 pt-10 md:max-w-none md:grid-cols-2 md:gap-8`}>
      <a className="group relative block" href={`/${post.full_slug}/`}>
        <div className="relative h-0 overflow-hidden rounded bg-gray-400 pb-[56.25%] shadow-lg md:h-72 md:pb-[75%] lg:pb-[56.25%]">
          <Image
            src={bannerImage.filename}
            alt={title}
            width={200}
            height={200}
            className="absolute inset-0 mb-6 h-full w-full rounded bg-gray-400 object-cover shadow-lg"
          />
        </div>
      </a>

      <div className="mt-2">
        <header>
          <div className="mb-1">
            <span className="text-sm">
              <span>{getFormattedDate(publishedDate)}</span> ·{' '}
              <a
                className="capitalize underline hover:text-primary"
                href={`/category/${category?.toLowerCase().replaceAll(' ', '-')}/`}
              >
                {category}
              </a>
            </span>
          </div>
          <h2 className="font-heading mb-2 text-xl font-bold leading-tight sm:text-2xl">
            <a
              className="transition duration-200 ease-in hover:text-primary"
              href={`/${post.full_slug}/`}
            >
              {title}
            </a>
          </h2>

          {description && <p className="text-offset flex-grow text-sm">{description}</p>}
          <footer className="mt-5">
            <ul className="text-sm">
              {tags?.map(({ name, _uid }) => <Tags name={name} key={_uid} />)}
            </ul>
          </footer>
        </header>
      </div>
    </article>
  );
};
