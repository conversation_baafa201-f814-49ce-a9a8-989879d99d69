import React from 'react';

import { Card, CardContent, CardTitle } from '@camped-ui/card';

export const CourseCard = ({ item }) => {
  return (
    <Card className="mb-3 h-full overflow-hidden" style={{ flex: 1 }}>
      <div className="h-28 w-full bg-secondary">
        {item?.imageUrl ? (
          <img src={item?.imageUrl} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
        ) : null}
      </div>
      <CardContent className="p-3">
        <CardTitle className="text-left text-base">{item?.name}</CardTitle>
      </CardContent>
    </Card>
  );
};
