import Link from 'next/link';

import { Button } from '@camped-ui/button';

interface EdgeCaseCardProps {
  title: string;
  description: string;
  ctaLabel?: string;
  ctaHref?: string;
}

export const EdgeCaseCard = ({ title, description, ctaLabel, ctaHref }: EdgeCaseCardProps) => {
  return (
    <div className="flex h-[450px] shrink-0 items-center justify-center rounded-md border border-dashed p-8">
      <div className="mx-auto flex max-w-[600px] flex-col items-center justify-center p-8 text-center">
        <br />
        <h3 className="mt-4 max-w-[400px] text-lg font-semibold">{title}</h3>
        <p className="mt-2 text-sm text-muted-foreground">{description}</p>
        <br />
        {ctaLabel && ctaHref && (
          <Button size="sm" className="relative" asChild>
            <Link href={ctaHref}>{ctaLabel}</Link>
          </Button>
        )}
      </div>
    </div>
  );
};
