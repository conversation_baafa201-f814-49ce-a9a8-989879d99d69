'use client';

import { usePathname } from 'next/navigation';

import { CardDescription } from '@camped-ui/card';

import { ResultCardWrapper } from './result-card-wrapper';

const options = [
  { value: 'code_quality', label: 'Code Quality' },
  { value: 'complexity', label: 'Complexity' },
  { value: 'efficiency', label: 'Efficiency' },
  { value: 'problem_solving', label: 'Problem Solving' },
  { value: 'readability', label: 'Readability' },
];

const MyFrontendInterviewCard = ({ interview, session }) => {
  const pathName = usePathname();
  const screen = pathName?.split('/')?.[1];
  const isStudentScreen = screen === 'student';
  const showPremium =
    Boolean((session?.premium as unknown as { plan?: string })?.plan) ||
    session?.memberships?.length > 0;
  return (
    <ResultCardWrapper
      time={interview?.createdAt}
      href={`${pathName}/${interview?.id}`}
      options={options}
      overallScore={interview?.feedback?.overall_score}
      feedback={interview?.feedback}
      showPremium={showPremium}
    >
      <CardDescription className="line-clamp-2 capitalize">
        <span className="font-bold">Question: </span>
        {interview?.questionId?.replaceAll(/-/g, ' ')}
      </CardDescription>
      {interview?.feedback?.short_summary ? (
        <CardDescription className="mt-2 line-clamp-2 text-justify">
          <span className="font-bold">Feedback: </span>
          <span
            dangerouslySetInnerHTML={{
              __html: interview?.feedback?.short_summary,
            }}
          />
        </CardDescription>
      ) : null}
    </ResultCardWrapper>
  );
};

export default MyFrontendInterviewCard;
