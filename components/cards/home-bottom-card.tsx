'use client';

import Image from 'next/image';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { Icon } from '@/icons';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';

import { Button } from '@camped-ui/button';
import { Card, CardTitle } from '@camped-ui/card';

import { formatTimestamp } from '../data-table/interview-candidate/columns';

const HomeBottomCard = ({ title, tabs, page, totalCount, screen }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const pageFilter = {
    'Recent Interviews': 'interviewPage',
    'Pending Feedback': 'feedbackPage',
    'Active Student': 'studentPage',
    'Recent Interviewathon': 'interviewathonPage',
  };
  const handlePageChange = (newPage) => {
    const paramKey = pageFilter[title];
    const nextSearchParams = new URLSearchParams(searchParams?.toString() ?? '');
    nextSearchParams.set(paramKey, newPage.toString());
    router.push(`${pathname}?${nextSearchParams.toString()}`);
  };

  const renderPaginationButtons = () => (
    <div className="flex items-center space-x-1">
      <Button
        aria-label="Go to first page"
        variant="outline"
        size="icon"
        className="hidden size-5 p-0 lg:flex"
        onClick={() => handlePageChange(1)}
        disabled={Number(page) === 1}
      >
        <DoubleArrowLeftIcon className="size-4" aria-hidden="true" />
      </Button>
      <Button
        aria-label="Go to previous page"
        variant="outline"
        size="icon"
        className="size-8"
        onClick={() => handlePageChange(Number(page) - 1)}
        disabled={Number(page) === 1}
      >
        <ChevronLeftIcon className="size-4" aria-hidden="true" />
      </Button>
      <Button
        aria-label="Go to next page"
        variant="outline"
        size="icon"
        className="size-8"
        onClick={() => handlePageChange(Number(page) + 1)}
        disabled={Number(page) === totalCount}
      >
        <ChevronRightIcon className="size-4" aria-hidden="true" />
      </Button>
      <Button
        aria-label="Go to last page"
        variant="outline"
        size="icon"
        className="hidden size-8 lg:flex"
        onClick={() => handlePageChange(Number(totalCount))}
        disabled={Number(page) === totalCount}
      >
        <DoubleArrowRightIcon className="size-4" aria-hidden="true" />
      </Button>
    </div>
  );

  return (
    <Card className="h-[60vh] w-[55vh] border-none shadow-none">
      <div className="mb-4 flex items-center justify-between">
        <CardTitle className="pl-4 pt-4 text-base font-semibold">{title}</CardTitle>
      </div>
      {tabs?.map((item, index) => (
        <>
          <div key={index} className="mb-2 h-[44vh]">
            <div
              className={`max-h-[44vh] ${item?.row?.length === 0 ? 'h-full' : ''} overflow-auto`}
            >
              {item?.row?.length !== 0 ? (
                <table className="w-full border-collapse">
                  <thead className="sticky top-0 z-10 bg-secondary text-left">
                    <tr>
                      {item?.header?.map((headerItem, headerIndex) => (
                        <th key={headerIndex} className="border-b p-2 text-xs font-normal">
                          {headerItem}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {item?.row?.map((rowItem, rowIndex) => (
                      <tr
                        key={rowIndex}
                        className="cursor-pointer p-2 text-xs"
                        onClick={() => {
                          if (screen === 'activeStudent') {
                            router.push(`/student/${rowItem?.id}`);
                          } else {
                            router.push(
                              `/${screen}/${rowItem?.eventId}/${rowItem?.id}?title=${rowItem?.interview}-${rowItem?.candidate}`,
                            );
                          }
                        }}
                      >
                        <td className="border-b p-2">
                          <div className="text-sm">{rowItem?.candidate}</div>
                          {rowItem?.interview}
                        </td>
                        <td className="border-b p-2 text-sm">
                          <div
                            className={`flex items-center gap-1 ${
                              rowItem?.recommendation
                                ? rowItem?.recommendation?.toLowerCase() === 'hire'
                                  ? 'text-green-500'
                                  : 'text-destructive'
                                : ''
                            }`}
                          >
                            {rowItem?.score === 0 || rowItem?.score ? `${rowItem?.score}` : '-'}
                            {rowItem?.recommendation && (
                              <Icon
                                name={
                                  rowItem?.recommendation?.toLowerCase() === 'hire'
                                    ? 'ThumbsUp'
                                    : 'ThumbsDown'
                                }
                                className={`h-4 w-4`}
                              />
                            )}
                          </div>
                        </td>
                        <td className="border-b p-2">
                          {rowItem?.time ? (
                            <>
                              <span className="text-sm">
                                {formatTimestamp(rowItem?.time)?.date}
                              </span>{' '}
                              {formatTimestamp(rowItem?.time)?.time}
                            </>
                          ) : (
                            rowItem?.practices
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="flex h-full w-full items-center justify-center">
                  <Image src="/noResult.png" alt="no result" width={344} height={344} />
                </div>
              )}
            </div>
          </div>

          {item?.row?.length !== 0 && totalCount > 1 && (
            <div className="mt-1 flex justify-between px-4">
              <div className="flex items-center justify-center text-sm font-medium">
                Page {page} of {totalCount}
              </div>
              {renderPaginationButtons()}
            </div>
          )}
        </>
      ))}
    </Card>
  );
};

export default HomeBottomCard;
