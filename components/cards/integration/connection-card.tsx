'use client';

import { useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { useSocialAccountStore } from '@/packages/shared-store';
import { getCookie } from '@/utils/cookies';

import { Button } from '@camped-ui/button';

export const SocialMenu = (props) => {
  const { connect, item, socialAccount } = props;
  const tenantId = getCookie('aceprepTenantId');

  const router = useRouter();

  const updateData = useSocialAccountStore((state) => (state as any).disconnect);

  return (
    <div className="relative flex flex-col justify-between rounded-xl border border-slate-200 bg-white p-4 text-left shadow-sm">
      <>
        {item?.hasConnect ? (
          <div className="absolute right-4 top-4 flex items-center rounded bg-slate-100 px-2 py-1 text-xs text-slate-500">
            {socialAccount?.accountId ? (
              <span className="relative mr-1 flex h-2 w-2">
                <span className="animate-ping-slow absolute inline-flex h-full w-full rounded-full bg-green-500 opacity-75"></span>
                <span className="relative inline-flex h-2 w-2 rounded-full bg-green-500"></span>
              </span>
            ) : (
              <span className="relative mr-1 flex h-2 w-2">
                <span className="relative inline-flex h-2 w-2 rounded-full bg-gray-400"></span>
              </span>
            )}
            {socialAccount?.accountId ? 'Connected' : 'Not connected'}
          </div>
        ) : null}
        <div className="flex justify-start">
          <Icon name={item?.iconName} className="mb-6 h-8 w-8" />
        </div>
        <h3 className="text-lg font-bold text-slate-800">{item?.platform}</h3>
        <p className="text-xs text-slate-500">{item?.description}</p>
      </>
      <div className="mt-4 flex space-x-2">
        {item?.hasConnect ? (
          <>
            {!socialAccount?.accountId ? (
              <Button
                onClick={() => connect({ tenantId })}
                className="rounded-md bg-primary px-4 py-2 text-sm font-normal text-white"
              >
                Connect
              </Button>
            ) : (
              <div className="flex gap-4">
                <Button
                  variant="destructive"
                  size="sm"
                  className="flex w-full items-center justify-start space-x-2 rounded-md text-left text-sm transition-all duration-75"
                  onClick={() =>
                    updateData(true, { id: socialAccount?.id, platform: item?.platform })
                  }
                >
                  Disconnect
                </Button>
                {item?.hasConfig && (
                  <Button
                    onClick={() =>
                      router.push(`/tenant/integration/${item?.platform?.toLowerCase()}`)
                    }
                    variant="outline"
                    size="sm"
                  >
                    Configure
                  </Button>
                )}
              </div>
            )}
          </>
        ) : (
          <>
            <Button
              onClick={() => router.push(`/tenant/integration/${item?.platform?.toLowerCase()}`)}
              size="sm"
            >
              Manage
            </Button>
          </>
        )}
      </div>
    </div>
  );
};
