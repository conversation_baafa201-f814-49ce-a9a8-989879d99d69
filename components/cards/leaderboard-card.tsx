'use client';

import Image from 'next/image';

import { Icon } from '@/icons';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Card, CardDescription, CardTitle } from '@camped-ui/card';

const LeaderBoardTopCard = () => {
  return (
    <Card className="m-2 flex h-[120px] items-center gap-8 p-4">
      <CardTitle className="text-left text-base">1</CardTitle>
      <Card className="h-20 w-20 rounded-full bg-secondary">
        <Avatar className="h-20 w-20 rounded-full">
          <AvatarImage src={'user?.image'} />
          <AvatarFallback>
            <Icon name="User" />
          </AvatarFallback>
        </Avatar>
      </Card>
      <div className="flex h-full flex-col justify-between">
        <div className="">
          <CardTitle className="text-left text-base">Hari</CardTitle>
          <CardDescription className="text-l p-0 text-left">Information Technology</CardDescription>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <CardTitle className="text-left text-base">Points :</CardTitle>
            <CardDescription className="text-l p-0 text-left">200</CardDescription>
          </div>
          <div className="flex items-center gap-1">
            <CardTitle className="text-left text-base">Total Participation:</CardTitle>
            <CardDescription className="text-l p-0 text-left">10</CardDescription>
          </div>
          <div className="flex items-center gap-1">
            <CardTitle className="text-left text-base">Average :</CardTitle>
            <CardDescription className="text-l p-0 text-left">20%</CardDescription>
          </div>
        </div>
      </div>
    </Card>
  );
};
const LeaderBoardCard = ({ position, data, isYour = false }) => {
  const icon = () => {
    if (position === 1) return '/Gold.png';
    if (position === 2) return '/Silver.png';
    if (position === 3) return '/Bronze.png';
  };

  return (
    <Card
      className={`flex h-[220px] w-full flex-col justify-between p-4 ${isYour ? 'bg-sky-100' : ''}`}
    >
      <div className="items-centers flex justify-between">
        <div className="flex flex-col gap-2">
          <Card className="h-16 w-16 rounded-full bg-secondary">
            <Avatar className="h-16 w-16 rounded-full">
              <AvatarImage
                src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${data?.userId}?date=${data?.user?.updatedAt}`}
              />
              <AvatarFallback>
                <Icon name="User" />
              </AvatarFallback>
            </Avatar>
          </Card>
          <div className="">
            <CardTitle className="text-left text-base">{data?.fullName}</CardTitle>
            <CardDescription className="text-l p-0 text-left">
              {data?.department ?? '-'}
            </CardDescription>
          </div>
        </div>
        <CardTitle className="text-left text-base">
          <div className="flex gap-4">
            {isYour && (
              <Badge variant="outline" className="bg-secondary">
                <p className="text-black">You</p>
              </Badge>
            )}
            {icon() ? (
              <div className="flex w-full justify-center">
                <Image src={`${icon()}`} alt="Badge" width={30} height={30} />
              </div>
            ) : (
              <p>{position}</p>
            )}
          </div>
        </CardTitle>
      </div>
      <div className="flex justify-between gap-4">
        <div className="items-center gap-1">
          <p className="text-sm">Points</p>
          <p className="text-left font-semibold">{data?.totalFinalScore ?? '-'}</p>
        </div>
        <div className="items-center gap-1">
          <p className="text-sm">Interviewathons</p>
          <p className="text-left font-semibold">
            {`${data?.unique_interviewathon ?? '-'}/${data?.totalInterviewathon ?? '-'}`}
          </p>
        </div>
        <div className="items-center gap-1">
          <p className="text-sm">Practices</p>
          <p className="text-left font-semibold">{`${data?.total_practices ?? '-'}`}</p>
        </div>
        <div className="items-center gap-1">
          <p className="text-sm">Average</p>
          <p className="text-left font-semibold">{data?.averageScore ?? '-'}</p>
        </div>
      </div>
    </Card>
  );
};

export { LeaderBoardTopCard, LeaderBoardCard };
