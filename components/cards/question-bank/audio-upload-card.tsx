'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import { Icon } from '@/icons';
import { AudioRecorder, useAudioRecorder } from 'react-audio-voice-recorder';
import { useDropzone } from 'react-dropzone';

import { But<PERSON> } from '@camped-ui/button';
import { Card, CardDescription, CardHeader } from '@camped-ui/card';
import { Input } from '@camped-ui/input';
import { Separator } from '@camped-ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@camped-ui/tabs';

import Modal from '../../Modal/modal';

export const AudioUploadTabs = ({ open, setOpen, form, onChange, referenceVideo }) => {
  const [videoBlob, setVideoBlob]: any = useState(null);
  const [fileName, setFileName]: any = useState('');
  const [selectedFile, setSelectedFile] = useState<FileList | null>(null);

  const getImageData = (event: any) => {
    const displayUrl = URL.createObjectURL(event[0]);
    const dataTransfer = new DataTransfer();
    Array.from(event).forEach((image) => dataTransfer.items.add(image as any));
    const files = dataTransfer.files;
    return { files, displayUrl };
  };

  //   const [imageGenerated, setImageGenerated] = useState(referenceVideo);
  const imageGenerated = referenceVideo?.filter((item) => item?.format === 'audio');

  const recorderControls = useAudioRecorder(
    {
      noiseSuppression: true,
      echoCancellation: true,
    },
    (err) => console.table(err), // onNotAllowedOrFound
  );

  const onDrop = useCallback((file) => {
    const acceptedFiles = file?.target?.files;
    if (acceptedFiles.length > 0) {
      const { files, displayUrl } = getImageData(acceptedFiles);
      form?.setValue('previewUrl', displayUrl);
      form?.setValue('videoFile', files);
      setSelectedFile(files);
    }
  }, []);

  useEffect(() => {
    if (!recorderControls?.recordingBlob) return;
    setVideoBlob(recorderControls.recordingBlob);
    const file = new File([recorderControls.recordingBlob], 'video.wav', { type: 'audio/wav' });
    const { files, displayUrl } = getImageData([file]);
    setSelectedFile(files);
    form?.setValue('videoFile', files);
    form?.setValue('previewUrl', displayUrl);
    recorderControls.recordingBlob = undefined;
  }, [recorderControls?.recordingBlob]);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'audio/mpeg': ['.mp3'],
      'audio/wav': ['.wav'],
      'audio/flac': ['.flac'],
      'audio/x-m4a': ['.m4a'],
    },
    onDrop,
  });

  const handleDelete = () => {
    if (recorderControls?.recordingBlob) recorderControls.recordingBlob = undefined;
    setVideoBlob(null);
    form?.setValue('previewUrl', null);
  };

  return (
    <Modal showModal={open} setShowModal={() => setOpen(null)}>
      <div className="mx-5 overflow-scroll rounded-lg bg-secondary p-4 shadow-lg sm:w-3/4 sm:overflow-hidden md:w-1/2">
        <Tabs defaultValue="device" className="w-full">
          <TabsList className="w-full">
            <TabsTrigger value="device" className="w-full">
              Upload from device
            </TabsTrigger>
            <TabsTrigger value="image-directory" className="w-full">
              Audio Directory
            </TabsTrigger>
            <TabsTrigger value="audio" className="w-full">
              Record Audio
            </TabsTrigger>
          </TabsList>
          <TabsContent value="device" className="mt-2 w-full border-none p-0">
            <div
              {...getRootProps()}
              className="space-2 flex h-80 w-full flex-wrap items-center justify-evenly gap-2 overflow-scroll rounded border-border p-4"
            >
              <label
                htmlFor="dropzone-file"
                className="dark:hover:bg-bray-900 flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-6 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-800 dark:hover:bg-gray-600"
              >
                {!form?.watch('previewUrl') && (
                  <div className="w-full text-center">
                    <div className="mx-auto w-10 rounded-md border p-2">
                      <Icon name="Upload" className="dark:text-slate-400" />
                    </div>

                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-semibold">Drag an Audio</span>
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-400">
                      Click to upload &#40; Audio should be under 20 MB &#41;
                    </p>
                  </div>
                )}

                {form?.watch('previewUrl') && (
                  <div className="self-center rounded text-center">
                    <audio src={form?.getValues('previewUrl')} controls />
                  </div>
                )}
              </label>

              <Input
                {...getInputProps()}
                type="file"
                onChange={onDrop}
                style={{ display: 'none' }}
              />
            </div>
          </TabsContent>
          <TabsContent value="image-directory" className="border-none p-0">
            <div className="space-2 flex h-96 w-full flex-wrap items-center justify-evenly gap-2 overflow-scroll rounded border border-slate-200 p-4 dark:border-slate-600">
              {imageGenerated && imageGenerated?.length !== 0 ? (
                imageGenerated?.map((image: any, index) => (
                  <div
                    key={index}
                    className={`rounded border border-slate-200 dark:border-slate-600 ${
                      form?.watch('referenceVideoUrl') === `${image?.url?.split('/')?.pop()}`
                        ? 'border-4 border-blue-600'
                        : ''
                    }`}
                    onClick={() => {
                      form?.setValue('referenceVideoUrl', image?.url?.split('/')?.pop());
                      setSelectedFile(null);
                    }}
                  >
                    <p className="p-2">{image?.url?.split('/')?.pop()?.split('_')?.[0]}</p>
                    <audio src={image?.url} controls />
                  </div>
                ))
              ) : (
                <div className="flex h-48 shrink-0 items-center justify-center rounded-md border border-dashed p-8">
                  <div className="mx-auto flex max-w-[600px] flex-col items-center justify-center p-8 text-center">
                    <br />
                    <h3 className="mt-4 max-w-[400px] text-lg font-semibold">No Audio Available</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Currently no audio available.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="audio" className="border-none p-0">
            <div className="space-2 flex w-full flex-col flex-wrap items-center justify-evenly gap-2 overflow-scroll rounded border border-slate-200 p-4 dark:border-slate-600">
              <Card>
                <CardHeader className="p-2">Please read this aloud</CardHeader>
                <Separator />
                <CardDescription className="p-2 text-sm">
                  In today&apos;s fast-paced digital world, technology plays a crucial role in
                  connecting people across the globe, enhancing communication, and driving
                  innovation in various industries. Artificial intelligence and automation are
                  revolutionizing the way businesses operate, making processes more efficient and
                  accessible.
                </CardDescription>
              </Card>
              {videoBlob ? (
                <audio src={URL.createObjectURL(videoBlob)} controls />
              ) : (
                <div className="w-[70%] flex-col items-center gap-2">
                  <label htmlFor="fileName" className="file-label mb-2 flex gap-2">
                    <p className="w-full max-w-[200px] cursor-pointer truncate">File Name</p>
                  </label>
                  <Input
                    id="fileName"
                    placeholder="Enter File Name"
                    type="text"
                    autoComplete="name"
                    autoCorrect="off"
                    value={fileName}
                    onChange={(e) => {
                      setFileName(e.target.value);
                    }}
                    required
                  />
                  <div className="mt-4 flex gap-4">
                    <Button
                      type="button"
                      onClick={
                        recorderControls.isRecording
                          ? recorderControls.stopRecording
                          : recorderControls.startRecording
                      }
                    >
                      <div className="flex items-center gap-x-2">
                        <Icon
                          name={recorderControls.isRecording ? 'Stop' : 'Camera'}
                          className="h-4 w-4"
                        />
                        {recorderControls.isRecording ? 'Stop Recording' : 'Start Recording'}
                      </div>
                    </Button>
                    <div className={recorderControls.isRecording ? '' : 'hidden'}>
                      <AudioRecorder
                        // onRecordingComplete={addAudioElement}
                        classes={{
                          AudioRecorderStartSaveClass: 'hidden',
                          AudioRecorderClass: 'bg-none',
                        }}
                        recorderControls={recorderControls}
                        showVisualizer={true}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
        <div className="mt-4 flex flex-1 justify-end gap-x-4">
          {(videoBlob || form?.getValues('previewUrl')) && (
            <Button size={'icon'} variant={'destructive'} onClick={handleDelete} type="button">
              <Icon name="Trash2" className="h-4 w-4" />
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={() => setOpen(null)}>
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={() => {
              onChange(selectedFile, true, fileName);
              setOpen(null);
            }}
          >
            Submit
          </Button>
        </div>
      </div>
    </Modal>
  );
};
