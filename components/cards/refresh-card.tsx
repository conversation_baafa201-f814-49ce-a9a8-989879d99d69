'use client';

import React from 'react';

import { useRouter } from 'next/navigation';

import { AppLogo } from '@/layout/logo';

import { Button } from '@camped-ui/button';

const RefreshCard = ({ description, href, showRefresh, buttonTitle }) => {
  const router = useRouter();
  return (
    <>
      <div className="mx-auto mt-[60px] flex max-w-lg flex-col items-center text-center">
        <AppLogo />

        {description && <p className="mt-4 text-gray-500">{description}</p>}
        {href && (
          <Button
            onClick={() => {
              router.replace(href);
            }}
            className="mt-4"
          >
            {buttonTitle}
          </Button>
        )}
        {showRefresh && (
          <Button
            onClick={() => {
              window.location.reload();
            }}
            className="mt-4"
          >
            Refresh
          </Button>
        )}
      </div>
    </>
  );
};

export default RefreshCard;
