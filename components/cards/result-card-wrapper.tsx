import Link from 'next/link';

import ProgressCircle from '@/components/ui/progress-circle';

import { Card, CardDescription, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';

export const ResultCardWrapper = ({
  time,
  options,
  children,
  href,
  overallScore,
  feedback,
  showPremium,
}) => {
  const formattedDate = new Date(time).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  });
  return (
    <Link href={href}>
      <Card className="cursor-pointer p-4">
        <div className="flex w-full flex-col items-start gap-4 md:flex-row">
          <ProgressCircle
            percent={overallScore}
            width={100}
            height={100}
            strokeWidth={6}
            textSize="xl"
          />
          <div className="flex w-full flex-col items-start">
            <CardTitle className={`mb-2 text-lg font-semibold`}>{formattedDate}</CardTitle>
            {children}

            {options?.length > 0 && showPremium ? (
              <>
                <Separator className="mb-1 mt-2" />
                <div className="grid w-full grid-cols-3 justify-between md:grid-cols-5">
                  {options?.map((item, i) => (
                    <StatusMenu key={i} title={item?.label} value={feedback?.[item?.value]} />
                  ))}
                </div>
              </>
            ) : null}
          </div>
        </div>
      </Card>
    </Link>
  );
};

const StatusMenu = ({ title, value }) => {
  let score;

  if (typeof value === 'object') {
    score = value?.score;
  } else if (typeof value === 'number') {
    score = value;
  } else {
    score = '-';
  }

  return (
    <div className="text-left">
      <CardDescription>{title}</CardDescription>
      <CardDescription className="font-bold">{score}</CardDescription>
    </div>
  );
};
