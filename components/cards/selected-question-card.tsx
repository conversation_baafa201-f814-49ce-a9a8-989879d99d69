import React from 'react';

import { interviewName } from '@/constants/interview';
import { Icon } from '@/icons';

import { Badge } from '@camped-ui/badge';
import { Card } from '@camped-ui/card';
import { FormControl, FormItem } from '@camped-ui/form';
import { Label } from '@camped-ui/label';

function SelectedQuestionCard(props) {
  const { index, field, handleRemove, onChange, codingLanguage } = props;

  if (field?.value?.round !== 'video-interview') {
    return (
      <Card className="relative mb-4 h-full p-2">
        <div className="flex justify-between gap-1 px-2">
          <div className="flex items-center gap-2">
            <p className="opacity-80">{index + 1}</p>
            <p>{interviewName[field?.value?.round]}</p>
          </div>
          <div onClick={() => handleRemove(index)} className="rounded-full bg-secondary p-1">
            <Icon name="Trash2" className="h-4 w-4 cursor-pointer" />
          </div>
        </div>
        <div className="m-2 rounded-md bg-secondary p-2">
          <p className="font-semibold">{field?.value?.question || field?.value?.title}</p>
          <Label className="mt-1 font-normal">{field?.value?.description}</Label>
        </div>
        <div className="mx-2 my-1 flex gap-x-2 gap-y-1">
          {field?.value?.round === 'coding-interview' && (
            <Badge variant="secondary">{codingLanguage}</Badge>
          )}
          {field?.value?.framework && (
            <Badge variant="secondary" className="capitalize">
              {field?.value?.framework.replaceAll(/-/g, ' ')}
            </Badge>
          )}
          {field?.value?.category && (
            <Badge variant="secondary">{field?.value?.categoryName}</Badge>
          )}
          {field?.value?.difficulty && (
            <Badge variant="secondary">{field?.value?.difficulty}</Badge>
          )}
          {field?.value?.time && field?.value.round !== 'multiple-choice' && (
            <Badge variant="secondary">{field?.value?.time} Mins</Badge>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className="relative mb-4 h-full p-2">
      <div className="flex justify-between gap-1 px-2">
        <div className="flex items-center gap-2">
          <p className="opacity-80">{index + 1}</p>
          <p>{interviewName[field?.value?.round]}</p>
        </div>
        <div onClick={() => handleRemove(index)} className="rounded-full bg-secondary p-1">
          <Icon name="Trash2" className="h-4 w-4 cursor-pointer" />
        </div>
      </div>
      <div className="m-2 rounded-md bg-secondary p-2">
        <FormItem className="flex h-full flex-row gap-2">
          <FormControl className="flex-[6] pb-2">
            <div className="flex flex-col gap-2">
              <Label className="font-semibold">Question</Label>
              <textarea
                placeholder="Enter question for this event"
                value={field?.value?.question}
                onChange={(e) => {
                  onChange(e.target.value, index);
                }}
                ref={field?.ref}
                onBlur={field?.onBlur}
                rows={2}
                className="resize-none rounded-md border-secondary p-2 text-sm"
              />
            </div>
          </FormControl>
        </FormItem>
      </div>
    </Card>
  );
}

export default SelectedQuestionCard;
