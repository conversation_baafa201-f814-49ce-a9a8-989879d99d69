'use client';

import { useRouter } from 'next/navigation';

import { Icon } from '@/icons';
import { useQueryClient } from '@tanstack/react-query';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

const textColors = {
  pink: 'text-[#C93AFB]',
  purple: 'text-[#6A57C7]',
  blue: 'text-[#5784C8]',
  turquoise: 'text-[#00A2C1]',
  lightpink: 'text-[#FF76DF]',
  orange: 'text-[#FF9900]',
  cyan: 'text-[#02CDFA]',
  green: 'text-[#7CBF63]',
  yellow: 'text-[#FECF23]',
};

const bgColors = {
  pink: 'bg-[#C93AFB]',
  purple: 'bg-[#6A57C7]',
  blue: 'bg-[#5784C8]',
  turquoise: 'bg-[#00A2C1]',
  lightpink: 'bg-[#FF76DF]',
  orange: 'bg-[#FF9900]',
  cyan: 'bg-[#02CDFA]',
  green: 'bg-[#7CBF63]',
  yellow: 'bg-[#FECF23]',
};

export const TemplateCard = ({ item }) => {
  let bgClasses = bgColors[item?.iconColor];

  return (
    <Card className="flex w-full flex-row items-start gap-4 space-y-4 p-4">
      <CardContent className="flex-1 p-0">
        <CardHeader className="mb-3 flex flex-row items-center gap-3 p-0">
          <div
            className={`flex h-[40px] w-[40px] items-center justify-center rounded ${bgClasses}`}
          >
            <Icon name={item.iconName} className="h-6 w-6 text-white" />
          </div>
        </CardHeader>
        <CardTitle className="m-0 text-xl">{item?.role}</CardTitle>
        <CardDescription>{item?.level}</CardDescription>
        <CardDescription className="mt-1 line-clamp-3">{item?.jobDescription}</CardDescription>
      </CardContent>
    </Card>
  );
};
