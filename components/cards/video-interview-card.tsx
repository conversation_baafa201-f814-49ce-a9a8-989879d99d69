'use client';

import { usePathname } from 'next/navigation';

import { CardDescription } from '@camped-ui/card';

import { ResultCardWrapper } from './result-card-wrapper';

const options = [
  { value: 'impact', label: 'Impact' },
  { value: 'clarity', label: 'Clarity' },
  { value: 'passion', label: 'Passion' },
  { value: 'confidence', label: 'Confidence' },
  { value: 'communication', label: 'Communication' },
];

const MyInterviewCard = ({ interview, session }) => {
  const pathName = usePathname();
  const screen = pathName?.split('/')?.[1];
  const isStudentScreen = screen === 'student';
  const showPremium =
    Boolean((session?.premium as unknown as { plan?: string })?.plan) ||
    session?.memberships?.length > 0;
  return (
    <ResultCardWrapper
      time={interview?.createdAt}
      href={`${pathName}/${interview?.id}`}
      options={options}
      overallScore={interview?.feedback?.overall_score}
      feedback={interview?.feedback}
      showPremium={showPremium}
    >
      <CardDescription className="line-clamp-2">
        <span className="font-bold">Question: </span>
        {interview?.question}
      </CardDescription>
      {interview?.feedback?.short_summary ? (
        <CardDescription className="mt-2 line-clamp-2 text-justify">
          <span className="font-bold">Feedback: </span>
          <span
            dangerouslySetInnerHTML={{
              __html: interview?.feedback?.short_summary,
            }}
          />
        </CardDescription>
      ) : null}
    </ResultCardWrapper>
  );
};

export default MyInterviewCard;
