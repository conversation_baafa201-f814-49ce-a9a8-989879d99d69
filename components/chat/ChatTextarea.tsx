'use client';

import React, { useEffect, useRef, useState } from 'react';

import { useHandleChatMessage } from '@/hooks/mutation/use-handle-chat-message';
import { Icon } from '@/icons';
import Textarea from 'react-textarea-autosize';

import { Button } from '@camped-ui/button';

const ChatTextarea = ({ setMessages }) => {
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [userInput, setUserInput] = useState('');

  const { mutate: handleSendMessage } = useHandleChatMessage({
    setIsLoading,
    setMessages,
    setUserInput,
  });

  useEffect(() => {
    if (textAreaRef.current === null) return;
    textAreaRef.current.focus();
  }, []);

  async function handleKeyDown(e: React.KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (isLoading) return;
      if (userInput.trim() === '') return;
      setMessages((message) => [...message, { role: 'user', content: userInput }]);
      await handleSendMessage({ message: userInput });
    }
  }

  return (
    <div className="mx-auto max-w-4xl px-3 lg:px-0">
      <div className="relative mt-1 rounded-md shadow-sm">
        <Textarea
          ref={textAreaRef}
          rows={1}
          name="comment"
          id="comment"
          className="w-full resize-none rounded-md bg-secondary px-4 py-3"
          placeholder="Type your message here..."
          defaultValue={''}
          value={userInput}
          onKeyDown={handleKeyDown}
          onChange={(e) => {
            setUserInput(e.target.value);
          }}
        />
        {isLoading && (
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
          </div>
        )}
        {!isLoading && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <Button
              className="mb-2"
              size="icon"
              disabled={userInput.trim() === '' || isLoading}
              onClick={async () => {
                if (isLoading) return;
                if (userInput.trim() === '') return;
                setMessages((message) => [...message, { role: 'user', content: userInput }]);
                await handleSendMessage({ message: userInput });
              }}
            >
              <Icon name="Send" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatTextarea;
