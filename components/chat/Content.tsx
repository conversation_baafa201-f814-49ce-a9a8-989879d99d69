'use client';

import React, { useEffect, useRef } from 'react';

import SystemMessage from '@/components/chat/SystemMessage';
import UserMessage from '@/components/chat/UserMessage';

const Content = ({ messages }) => {
  const lastMessageRef: any = useRef(null);
  // Scroll to the last message whenever the messages change
  useEffect(() => {
    if (lastMessageRef.current) {
      lastMessageRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  return (
    <div className="relative mt-[80px] flex-1 overflow-y-auto">
      {messages.length > 0 ? (
        <div className="absolute top-0 flex w-full flex-col overflow-y-auto overflow-x-hidden">
          {messages.map((message, index) => (
            <div key={index} ref={index === messages.length - 1 ? lastMessageRef : null}>
              {message.role === 'user' ? (
                <UserMessage message={message.content} />
              ) : (
                <SystemMessage message={message.content} />
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="my-32 flex flex-col items-center justify-center">
          <h1 className="text-3xl font-bold">Luma</h1>
          <p className="mt-2 text-gray-400">`</p>
        </div>
      )}
    </div>
  );
};

export default Content;
