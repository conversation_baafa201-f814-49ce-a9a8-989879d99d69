'use client';

import React from 'react';

import { Icon } from '@/icons';
import { getCookie } from '@/utils/cookies';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';

type UserMessageProps = {
  message: string;
};

const UserMessage = ({ message }: UserMessageProps) => {
  const userId = getCookie('aceprepUserId');
  return (
    <div className="">
      <div className="mx-auto w-full max-w-4xl">
        <div className="flex justify-end gap-4 px-3 py-4 lg:px-0">
          <div className="rounded-b-md rounded-tl-md bg-secondary p-2">{message}</div>
          <Avatar className="h-10 w-10 rounded-md">
            <AvatarImage
              src={`${process.env.NEXT_PUBLIC_CAMPED_ACCOUNTS_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_ACCOUNTS_ENVIRONMENT}/user-profile/${userId}`}
              alt="@camped"
            />
            <AvatarFallback>
              <Icon name="User" className="h-6 w-6" />
            </AvatarFallback>
          </Avatar>
        </div>
      </div>
    </div>
  );
};

export default UserMessage;
