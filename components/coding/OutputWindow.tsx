import React from 'react';

const OutputWindow = (props) => {
  const { outputDetails, className } = props;
  const getOutput = () => {
    let statusId = outputDetails?.status?.id;

    if (statusId === 6) {
      // compilation error
      return (
        <pre className="h-full overflow-scroll whitespace-pre-wrap px-2 py-1 text-xs font-normal text-red-500">
          {atob(outputDetails?.compile_output)}
        </pre>
      );
    } else if (statusId === 3) {
      return (
        <pre className="h-full overflow-scroll whitespace-pre-wrap px-2 py-1 text-xs font-normal">
          {atob(outputDetails.stdout) !== null ? `${atob(outputDetails.stdout)}` : null}
        </pre>
      );
    } else if (statusId === 5) {
      return (
        <pre className="h-full overflow-scroll whitespace-pre-wrap px-2 py-1 text-xs font-normal text-red-500">
          {`Time Limit Exceeded`}
        </pre>
      );
    } else {
      return (
        <pre className="h-full overflow-scroll whitespace-pre-wrap px-2 py-1 text-xs font-normal text-red-500">
          {atob(outputDetails?.stderr)}
        </pre>
      );
    }
  };
  return (
    <>
      <div
        className={`h-full w-full overflow-y-auto rounded-md border-0 text-left text-sm font-normal ${className}`}
      >
        {outputDetails ? <>{getOutput()}</> : null}
      </div>
    </>
  );
};

export default OutputWindow;
