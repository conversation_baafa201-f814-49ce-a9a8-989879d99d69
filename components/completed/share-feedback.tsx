import React, { useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { createFeedback } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import OnboardingTemplate from '../onboarding-stepper/onboarding-template';

const ShareFeedback = ({ setStep, setFeedbackSubmitted, email }) => {
  const form = useForm();
  const searchParams = useSearchParams();
  const tenantId = getCookie('aceprepTenantId');
  const userId = getCookie('aceprepUserId');
  const id = searchParams?.get('id');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleFeedbackSubmit = async (data) => {
    const isFilled = Object.values(data).some((item) => item === undefined);

    if (isFilled) {
      toast.remove();
      toast.error('Please fill all the fields');
      return;
    }

    toast.loading('Submitting the Feedback');
    setIsLoading(true);

    const feedback = await createFeedback({
      details: { eventId: id, tenantId: tenantId ?? '' },
      userId,
      feedback: data,
    });
    toast.remove();
    setIsLoading(false);
    if (feedback?.status === 200) {
      toast.success('Feedback Submitted Successfully');
      if (process.env.NEXT_PUBLIC_PLATFORM === 'hire') {
        router.push(process.env.NEXT_PUBLIC_ACEPREP_URL || 'https://aceprep.camped.academy');
      } else {
        router.push('/');
      }
    } else {
      toast.error(feedback?.error);
    }
  };

  return (
    <div className="mx-8 flex flex-col justify-center gap-4 px-16 pb-8">
      <OnboardingTemplate
        title="Feedback"
        description={
          'Thank you for completing the assessment! Our team will notify you with your assessment updates. We truly appreciate your feedback and insights.'
        }
      />
      <div className="mt-4 flex flex-col justify-center space-y-4">
        <div style={{ position: 'relative', height: '80dvh', overflow: 'auto' }}>
          <iframe
            src={`${process.env.NEXT_PUBLIC_FORM_URL}/s/cm74bpv8l0001dod0u7f3g7n0?career_practice_id=${id}&email=${email}&from_url=${process.env.NEXT_PUBLIC_API_BASE_URL}`}
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              width: '100%',
              height: '100%',
              border: 0,
            }}
          ></iframe>
        </div>
      </div>
    </div>
  );
};

export default ShareFeedback;
