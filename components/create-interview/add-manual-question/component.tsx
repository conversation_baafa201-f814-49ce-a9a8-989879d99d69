import { useEffect, useRef, useState } from 'react';

import { SelectedQuestionCard } from '@/components/cards';
import LanguagesDropdown from '@/components/coding/LanguagesDropdown';
import { interviewTypes } from '@/constants/interview';
import { Icon } from '@/icons';
import { createAIQuestions } from '@/services/apicall';
import { capitalizeFirstLetter } from '@/utils/string.helper';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';

import { Button } from '@camped-ui/button';
import { Dialog, DialogContent, DialogFooter } from '@camped-ui/dialog';
import { TagInput } from '@camped-ui/tag-input';

import { QuestionSection } from '../../interview/codingProblemModal';
import { DropDown } from '../dropdown';
import { useInterviewForm } from '../provider';

export const SelectQuestionHeader = ({ roundSelected, setRoundSelected }) => {
  return (
    <div className="flex items-start justify-between">
      <h1 className="text-xl font-semibold">Select Questions</h1>
      <div className="w-48">
        <DropDown
          disabled={false}
          data={interviewTypes}
          onSelect={(item) => setRoundSelected(item)}
          value={roundSelected?.name}
          placeHolder="Interview Type"
        />
      </div>
    </div>
  );
};

export const SelectQuestion = ({
  questions,
  setQuestions,
  language,
  setLanguage,
  codingProblems,
  frontendProblems,
  multipleChoiceCategory,
  multipleChoice,
  setMultipleChoice,
  multipleChoiceTopics,
  aiQuestions,
  setAIQuestions,
  MCQQuestion,
  setMCQQuestion,
  videoCategory,
  selectedVideoCategory,
  setSelectedVideoCategory,
}) => {
  const videoInterviewQuestions = [];
  const context = useInterviewForm();
  const form = (context as any)?.roleForm;
  const [loading, setLoading] = useState(false);
  const [selectedTopic, setSelectedTopic]: any = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [roundSelected, setRoundSelected] = useState(interviewTypes[0]);

  const questionContainerRef = useRef(null) as any;

  const handleRemove = (fieldIndex) => {
    const finalData = questions?.filter((_, index) => index !== fieldIndex);
    setQuestions(finalData);
  };
  const getTime = (round, item) => {
    if (round === 'video-interview') return 2;
    if (round === 'frontend-interview' || round === 'coding-interview') return item?.time;
    if (round === 'multiple-choice') return 1;
  };
  const topic = multipleChoiceTopics?.filter((item) => item?.category === multipleChoice?.slug);

  const onSelectChange = (sl) => {
    if (roundSelected?.value === 'coding-interview') {
      setLanguage(sl);
      const updatedQuestions = questions?.map((item) => {
        if (item?.round === 'coding-interview') {
          const languageCode =
            (item as any)?.template?.find((templateItem) => templateItem?.languageId === sl?.id)
              ?.code || '';
          return { ...item, source_code: languageCode, languages: [sl?.id] };
        }
        return item;
      });
      setQuestions(updatedQuestions);
    } else if (roundSelected?.value === 'multiple-choice') {
      setMultipleChoice(sl);
    } else if (roundSelected?.value === 'video-interview') {
      setSelectedVideoCategory(sl);
    }
  };

  useEffect(() => {
    if (questionContainerRef.current && questionContainerRef.current.lastChild) {
      setTimeout(() => {
        const lastField = questionContainerRef?.current?.lastChild;
        lastField?.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest',
        });
      }, 300);
    }
  }, [questions]);

  const problems = () => {
    switch (roundSelected?.value) {
      case 'coding-interview':
        return codingProblems;
      case 'frontend-interview':
        return frontendProblems;
      case 'multiple-choice':
        return MCQQuestion;
      default:
        return videoInterviewQuestions;
    }
  };
  const onDragEnd = (result) => {
    if (!result.destination) return;

    const reorderedQuestions = Array.from(questions);
    const [movedItem] = reorderedQuestions.splice(result.source.index, 1);
    reorderedQuestions.splice(result.destination.index, 0, movedItem);

    setQuestions(reorderedQuestions);
  };
  return (
    <div className="grid h-full grid-cols-1 gap-4 md:grid-cols-2">
      <div className="flex flex-col gap-2">
        <div
          className={`flex items-center ${
            ['coding-interview', 'multiple-choice']?.includes(roundSelected?.value)
              ? 'gap-1'
              : 'justify-between'
          }`}
        >
          <DropDown
            disabled={false}
            data={interviewTypes}
            onSelect={(item) => setRoundSelected(item)}
            value={roundSelected?.name}
            placeHolder="Interview Type"
            className="w-full max-w-[40%]"
          />
          {['coding-interview', 'multiple-choice', 'video-interview']?.includes(
            roundSelected?.value,
          ) && (
            <div className="w-full max-w-[50%]">
              <LanguagesDropdown
                languages={
                  roundSelected?.value === 'multiple-choice'
                    ? multipleChoiceCategory
                    : roundSelected?.value === 'video-interview'
                    ? videoCategory
                    : []
                }
                onSelectChange={onSelectChange}
                value={
                  roundSelected?.value === 'multiple-choice'
                    ? multipleChoice?.name
                    : roundSelected?.value === 'video-interview'
                    ? selectedVideoCategory?.name
                    : capitalizeFirstLetter(language?.value)
                }
              />
            </div>
          )}
          {(['video-interview', 'multiple-choice']?.includes(roundSelected?.value) || loading) && (
            <Button
              variant="link"
              disabled={loading}
              className="m-0 p-2"
              onClick={async () => {
                setLoading(true);
                if (roundSelected?.value?.toLowerCase() === 'video-interview') {
                  const { role, level, jobDescription } = form.getValues();

                  const questions = await createAIQuestions({
                    role,
                    level,
                    jobDescription,
                  });

                  setAIQuestions([
                    ...questions?.map((item) => ({ ...item, slug: 'AI' })),
                    ...aiQuestions,
                  ]);
                } else if (roundSelected?.value?.toLowerCase() === 'multiple-choice') {
                  setShowModal(true);
                }
                setLoading(false);
              }}
            >
              <Icon name="Sparkles" className="mr-2 h-4 w-4" /> AI
            </Button>
          )}
        </div>
        <div className="rounded-lg border p-4">
          <QuestionSection
            onSelect={(item) => {
              setQuestions((prevValue) => [
                ...prevValue,
                {
                  ...item,
                  questionId: item?.id,
                  question: item?.title ?? item?.question,
                  questionDescription: item?.description,
                  source_code:
                    (roundSelected?.value === 'coding-interview' &&
                      (item as any)?.template?.find((item) => item?.languageId === language?.id)
                        ?.code) ||
                    '',
                  languages: [language?.id ?? 63],
                  round: roundSelected?.value,
                  time: getTime(roundSelected?.value, item),
                },
              ]);
            }}
            problems={problems()}
            round={roundSelected?.value}
            multipleChoice={multipleChoice}
            questions={questions}
            form={form}
            setShowModal={setShowModal}
            loading={loading}
            multipleChoiceCategory={multipleChoiceCategory}
            aiQuestions={aiQuestions}
            selectedVideoCategory={selectedVideoCategory}
          />
        </div>
      </div>

      <div className="flex w-full flex-col gap-4">
        <div className="rounded-lg border bg-secondary p-4">
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="questions">
              {(provided) => (
                <div
                  className="h-[48vh] max-h-[48vh] space-y-4 overflow-y-scroll"
                  ref={(el) => {
                    provided.innerRef(el); // Connect Droppable with ref
                    questionContainerRef.current = el; // Also connect your custom ref
                  }}
                  {...provided.droppableProps}
                >
                  {questions?.map((value, index) => (
                    <Draggable key={index} draggableId={`question-${index}`} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          <SelectedQuestionCard
                            index={index}
                            field={{ value }}
                            handleRemove={(indexValue) => handleRemove(indexValue)}
                            codingLanguage={capitalizeFirstLetter(language?.value)}
                            onChange={(value, index) =>
                              setQuestions((prevQuestions) => {
                                const updatedQuestions = [...prevQuestions];
                                updatedQuestions[index] = {
                                  ...updatedQuestions[index],
                                  question: value,
                                };
                                return updatedQuestions;
                              })
                            }
                          />
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      </div>
      <Dialog
        open={showModal}
        onOpenChange={() => {
          setShowModal(false);
          setSelectedTopic([]);
        }}
      >
        <DialogContent>
          <div className="flex w-full flex-col items-center justify-center space-y-3 bg-background pt-2">
            <p className="text-xl font-bold">Select topic to generate question</p>

            <div className="flex w-full gap-4">
              <TagInput
                placeholder="Enter a topic"
                tags={selectedTopic ?? []}
                enableAutocomplete
                restrictTagsToAutocompleteOptions
                autocompleteOptions={topic?.map((item) => {
                  return {
                    ...item,
                    text: item?.name,
                  };
                })}
                className="sm:min-w-[450px]"
                setTags={(newTags: any) => {
                  setSelectedTopic(newTags);
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              disabled={loading || selectedTopic?.length === 0}
              className="m-0 p-2"
              onClick={async () => {
                setLoading(true);
                const questions = await createAIQuestions({
                  category: multipleChoice?.slug,
                  topics: selectedTopic?.map((item) => item.name),
                });
                setMCQQuestion([...questions, ...MCQQuestion]);
                setSelectedTopic([]);
                setShowModal(false);
                setLoading(false);
              }}
            >
              {loading ? (
                <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Icon name="Sparkles" className="mr-2 h-4 w-4" />
              )}{' '}
              Generate
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
