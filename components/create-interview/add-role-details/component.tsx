'use client';

import { useState } from 'react';

import { ContextualHelp } from '@/components/create-interview/contextual-help';
import { Icon } from '@/icons';
import { checkInterviewName, writeJD } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import ReactMarkdown from 'react-markdown';

import { Button } from '@camped-ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Input } from '@camped-ui/input';
import { Tooltip, TooltipContent, TooltipTrigger } from '@camped-ui/tooltip';

import { DropDown } from '../dropdown';
import { useInterviewForm } from '../provider';

export const AddRoleDetails = ({ level, isInterviewthon, eventId }) => {
  const orgId = getCookie('aceprepTenantId');
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(true);
  const context = useInterviewForm();
  const form = (context as any)?.roleForm;

  const validate = () => {
    const { role, level } = form.getValues();
    if (loading) return true;
    if (!role || !level) return true;
    return false;
  };
  const handleCheckInterviewName = async () => {
    const { name } = form.getValues();

    return await checkInterviewName(name, orgId);
  };

  return (
    <Form {...(form as any)} className="h-full w-full">
      <div className="grid w-full grid-cols-1 gap-8 px-1 md:grid-cols-2">
        <FormField
          control={form.control as any}
          name="name"
          render={({ field }) => {
            return (
              <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                <div className="flex flex-[1] items-center gap-2">
                  <FormLabel htmlFor="name" className="text-base">
                    Interview Name
                  </FormLabel>
                  <ContextualHelp
                    title="Interview Name"
                    content="Give your interview a descriptive name that helps you identify it later. For example: 'Frontend Developer - React' or 'Marketing Manager Q3 2023'."
                  />
                </div>
                <FormControl className="flex-[6]">
                  <Input
                    placeholder="Enter Interview Name"
                    {...field}
                    onBlur={async () => {
                      field.onBlur();
                      const checkName = await handleCheckInterviewName();
                      if (!checkName) {
                        form.setError('name', {
                          type: 'manual',
                          message: `${
                            isInterviewthon ? 'Interviewathon' : 'Interview'
                          } name already exists`,
                        });
                      } else {
                        form.clearErrors('name');
                      }
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
        <FormField
          control={form.control as any}
          name="role"
          render={({ field }) => {
            return (
              <FormItem className="flex h-20 w-full flex-col items-start">
                <FormLabel className="flex w-full flex-[1] flex-row items-center justify-between gap-2 text-base">
                  Role{' '}
                  <Tooltip className="cursor-pointer">
                    <TooltipTrigger>
                      <Icon name="info" className="mr-1 h-4 w-4 text-base font-normal opacity-50" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>The role will be shown to the candidate</p>
                    </TooltipContent>
                  </Tooltip>
                </FormLabel>
                <FormControl className="flex-[6]">
                  <Input placeholder="Enter Role Name" {...field} required />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        <FormField
          control={form.control as any}
          name="level"
          render={({ field }) => {
            return (
              <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                <div className="flex flex-[1] items-center gap-2">
                  <FormLabel htmlFor="level" className="text-base">
                    Level
                  </FormLabel>
                  <ContextualHelp
                    title="Experience Level"
                    content="Select the appropriate experience level for this position. This helps tailor the difficulty of questions."
                  />
                </div>
                <FormControl className="flex-[6]">
                  <DropDown
                    disabled={false}
                    data={level}
                    onSelect={(item) => {
                      field.onChange(item.displayName);
                      form.setValue('jobDescription', '');
                    }}
                    value={field?.value}
                    placeHolder="Select Level"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        <FormField
          control={form.control as any}
          name="duration"
          render={({ field }) => {
            const { errors } = form.formState;
            return (
              <FormItem className="relative col-span-1 flex h-20 w-full flex-col items-start">
                <FormLabel className="flex-[1] text-base">
                  Duration <span className="text-sm opacity-40">(in mins)</span>
                </FormLabel>
                <FormControl className="flex-[6]">
                  <Input
                    placeholder="Enter Duration"
                    type="number"
                    pattern="^[0-9]+$"
                    className="[appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                    {...field}
                    required
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {!isInterviewthon && (
          <FormField
            control={form.control as any}
            name="linkValidity"
            render={({ field }) => {
              return (
                <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                  <FormLabel htmlFor="linkValidity" className="flex-[1] text-base">
                    Link Expiration <span className="text-sm opacity-40">(in hours)</span>
                  </FormLabel>
                  <FormControl className="flex-[6]">
                    <Input placeholder="Enter Link validity" {...field} required className="" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        )}
        <FormField
          control={form.control as any}
          name="employment_type"
          render={({ field }) => {
            return (
              <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                <div className="flex flex-[1] items-center gap-2">
                  <FormLabel htmlFor="employment_type" className="text-base">
                    Employment Type
                  </FormLabel>
                  <ContextualHelp
                    title="Employment Type"
                    content="Select the type of employment for this position."
                  />
                </div>
                <FormControl className="flex-[6]">
                  <DropDown
                    disabled={false}
                    data={[
                      { displayName: 'Sales', value: 'Sales' },
                      { displayName: 'Apprentice', value: 'Apprentice' },
                      { displayName: 'Intern', value: 'Intern' },
                      { displayName: 'Piecework', value: 'Piecework' },
                      { displayName: 'Commission', value: 'Commission' },
                      { displayName: 'Contract', value: 'Contract' },
                      { displayName: 'Probation', value: 'Probation' },
                      { displayName: 'Part-time', value: 'Part-time' },
                      { displayName: 'Full-time', value: 'Full-time' },
                    ]}
                    onSelect={(item) => {
                      field.onChange(item.value);
                    }}
                    value={field?.value}
                    placeHolder="Select Employment Type"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
        <FormField
          control={form.control as any}
          name="evaluation"
          render={({ field }) => {
            return (
              <FormItem className="col-span-1 flex h-20 w-full flex-col items-start">
                <FormLabel htmlFor="level" className="flex-[1] text-base">
                  AI Feedback evaluation level
                </FormLabel>
                <FormControl className="flex-[6]">
                  <DropDown
                    disabled={false}
                    data={[
                      { displayName: 'Easy', value: 'easy' },
                      { displayName: 'Medium', value: 'medium' },
                      { displayName: 'Strict', value: 'hard' },
                    ]}
                    onSelect={(item) => {
                      field.onChange(item.value);
                    }}
                    value={field?.value ?? 'Strict'}
                    placeHolder="Select Level"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />
      </div>
      <FormField
        control={form.control as any}
        name="jobDescription"
        render={({ field }) => {
          const { errors } = form.formState;
          return (
            <FormItem className="relative col-span-1 mt-5 flex w-full flex-col items-start overflow-y-auto p-2">
              <div className="flex w-full items-center">
                <div className="flex w-full items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FormLabel className="text-base">Job Description</FormLabel>
                    <ContextualHelp
                      title="Job Description"
                      content="Provide a detailed job description to help generate relevant interview questions. The more specific you are, the better the questions will be."
                      placement="right"
                    />
                  </div>
                  {field.value && (
                    <Button variant="link" onClick={() => setIsEdit(!isEdit)}>
                      {isEdit ? 'Preview' : 'Edit'}
                    </Button>
                  )}
                </div>
                {loading ? (
                  <Icon name="Loader2" className="h-5 w-5 animate-spin text-blue-700" />
                ) : (
                  <></>
                )}
                <Button
                  variant="link"
                  disabled={validate()}
                  onClick={async () => {
                    const { role, level, jobDescription } = form.getValues();
                    setLoading(true);
                    setIsEdit(false);
                    let generatedContent;
                    generatedContent = await writeJD(role, level, jobDescription);
                    let chunks = '';
                    if (generatedContent) {
                      const reader = (generatedContent as any).getReader();

                      while (true) {
                        const { done, value } = await reader.read();
                        if (done) {
                          setLoading(false);
                          break;
                        }

                        // Assuming the value is of type Uint8Array
                        const textChunk = new TextDecoder().decode(value);

                        chunks += textChunk.includes('[DONE]')
                          ? textChunk?.replace('[DONE]', '')
                          : textChunk;

                        form.setValue('jobDescription', chunks);
                      }

                      setLoading(false);
                    }
                  }}
                >
                  Write with AI
                </Button>
              </div>
              <FormControl className="flex-[6]">
                {!isEdit ? (
                  <div className="prose prose-sm" style={{ overflowY: 'auto' }}>
                    <ReactMarkdown className="markDown m-4">
                      {field?.value?.replace(/\\n/g, '\n')}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <textarea
                    rows={5}
                    placeholder="Enter Job Description"
                    className="h-[350px] w-full overflow-y-auto rounded-md border bg-inherit p-2 text-sm focus:outline-2 focus:outline-offset-4 focus:outline-primary"
                    {...field}
                  />
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    </Form>
  );
};
