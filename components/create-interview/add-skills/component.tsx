'use client';

import { expertiseLevels } from '@/constants/interview';

import { FormControl, FormField, FormItem, FormLabel } from '@camped-ui/form';
import { Input } from '@camped-ui/input';

import { AddFields } from '../add-fileds';
import { DropDown } from '../dropdown';

export const AddSkills = ({ form, fields, setFields, eventId }) => {
  const { skills } = form.getValues();

  const handleRemoveFields = (index) => {
    const newSkills = skills?.filter((_, i) => i !== index);
    form.setValue('skills', newSkills);

    const newFields = [...fields];
    newFields?.splice(index, 1);
    setFields(newFields);
  };

  return (
    <div className="h-80">
      <AddFields
        context="Add Skills"
        buttonName="Add"
        fields={fields}
        setFields={setFields}
        className="mb-6 mt-4 h-72"
        handleRemoveFields={handleRemoveFields}
      >
        {(index) => (
          <>
            <p className="pb-10">{index + 1}</p>
            <div className="grid w-full grid-cols-2 gap-8">
              <FormField
                control={form.control as any}
                name={`skills[${index}].skill`}
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col items-start">
                    <FormLabel htmlFor={`skills[${index}].skill`} className="flex-[1] text-base">
                      Enter Skill
                    </FormLabel>
                    <FormControl className="flex-[6]">
                      <Input placeholder="Skill" {...field} value={skills?.[index]?.skill} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control as any}
                name={`skills[${index}].level`}
                render={({ field }) => (
                  <FormItem className="col-span-1 flex h-8 w-full flex-col items-start">
                    <FormLabel htmlFor={`skills[${index}].level`} className="flex-[1] text-base">
                      Level
                    </FormLabel>
                    <FormControl className="flex-[6]">
                      <DropDown
                        disabled={!!eventId}
                        data={expertiseLevels}
                        onSelect={(item) => field.onChange(item)}
                        value={skills?.[index]?.level?.name}
                        placeHolder="Select Level"
                        className="h-9"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </>
        )}
      </AddFields>
    </div>
  );
};
