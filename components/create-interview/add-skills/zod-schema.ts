import { z } from 'zod';

export const addSkillsSchema = z.object({
  skills: z.array(
    z
      .object({
        skill: z.string().min(3).max(100).optional(),
        level: z
          .object({
            name: z.string().min(1).optional(),
            value: z.string().min(1).optional(),
          })
          .optional(),
      })
      .refine(
        (obj) => {
          if ((obj.skill && !obj.level) || (!obj.skill && obj.level)) {
            return false;
          }
          return true;
        },
        { message: 'Both Skill and Level are required for each entry.' },
      ),
  ),
});
