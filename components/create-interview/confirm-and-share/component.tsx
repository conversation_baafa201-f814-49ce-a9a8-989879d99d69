import { useState } from 'react';

import Image from 'next/image';

import { Questions } from '@/components/Modal/interview-events-question';
import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';

import confirmAndShareImage from '../../../public/interview-flow/confirmAndShare.png';
import { useInterviewForm } from '../provider';

export const ConfirmAndShareHeader = ({ isLoading, setShow }) => {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-xl font-semibold">Confirm and Share</h1>
      <Button
        variant="link"
        type="button"
        onClick={() => setShow(true)}
        className="p-0"
        disabled={isLoading}
      >
        Preview
      </Button>
    </div>
  );
};

const Fields = ({ title, value }) => {
  const [show, setShow] = useState(false);
  return (
    <div>
      <p className="font-normal text-gray-500">{title}</p>
      <p className="font-semibold text-black">{value || '-'}</p>
    </div>
  );
};

export const ConfirmAndShare = ({
  setStep,
  isLoading,
  show,
  setShow,
  questions,
  questionType,
  aiQuestionCount,
  isInterviewthon,
}) => {
  const context = useInterviewForm();
  const { roleForm } = context as any;
  const interviewData = roleForm.getValues();

  let finalTime = 0;
  questions.map((question) => {
    finalTime += question.time ?? 2;
  });

  return (
    <>
      <div className="relative">
        <div className="ml-8">
          {finalTime && (
            <p
              className={`mb-2 flex items-center rounded-md bg-secondary p-2 text-sm font-normal leading-6`}
            >
              The average time to complete this interview is {finalTime} minutes. Please adjust the
              interview duration as needed for your requirements.
            </p>
          )}
          <Image
            src={confirmAndShareImage}
            alt="Confirm and Share"
            width={149}
            height={159}
            className="top-176 left-619 flex justify-center"
          />

          <p className="mb-4 mt-4 text-base font-normal leading-6">
            You have successfully created the interview. Now go ahead <br /> and share it with
            invited candidates.
          </p>

          <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2">
            <Fields title="Interview Name" value={interviewData?.name} />
            <Fields title="Role" value={interviewData?.role} />
            <Fields title="Interview Duration" value={`${interviewData?.duration} minutes`} />
          </div>
        </div>
      </div>
      <Questions
        eventDetails={{
          questions,
          isAiQuestion: questionType?.isAiQuestion,
          aiQuestionCount: aiQuestionCount?.aiQuestionCount,
          level: interviewData?.level,
          role: interviewData?.role,
          jobDescription: interviewData?.jobDescription,
          timing: { duration: interviewData?.duration, linkValidity: interviewData?.linkValidity },
        }}
        show={show}
        screen={isInterviewthon ? 'interviewathon' : 'interview'}
        setShow={() => setShow(false)}
      />
    </>
  );
};
