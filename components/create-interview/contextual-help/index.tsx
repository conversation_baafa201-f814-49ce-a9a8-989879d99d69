'use client';

import React from 'react';
import { Icon } from '@/icons';

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@camped-ui/tooltip';

interface ContextualHelpProps {
  title: string;
  content: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

export const ContextualHelp: React.FC<ContextualHelpProps> = ({
  title,
  content,
  placement = 'top',
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button type="button" className="inline-flex items-center justify-center rounded-full text-gray-500 hover:text-gray-700 focus:outline-none">
          <Icon name="HelpCircle" className="h-4 w-4" />
          <span className="sr-only">Help</span>
        </button>
      </TooltipTrigger>
      <TooltipContent side={placement} className="max-w-xs">
        <div className="space-y-1">
          <p className="font-medium">{title}</p>
          <p className="text-sm text-gray-500">{content}</p>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};
