import { useCallback } from 'react';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { bulkUploadTemplate } from '@/constants/interview';
import { emailPattern } from '@/constants/pattern';
import { Icon } from '@/icons';
import {
  downloadTemplate,
  filterValues,
  getUploadedFileData,
  validateFileUploaded,
} from '@/utils/excel-sheet.helper';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Form } from '@camped-ui/form';
import { Input } from '@camped-ui/input';

import CandidateList from '../../organization/candidate-list';
import { useInterviewForm } from '../provider';

export const InviteCandidatesHeader = ({ inviteMethod, setInviteMethod }) => {
  return (
    <div className="flex items-start justify-between">
      <h1 className="text-xl font-semibold">Invite Candidates</h1>
      <Tabs defaultValue={inviteMethod}>
        <TabsList className="grid w-[320px] grid-cols-2">
          <TabsTrigger value="manual" onClick={() => setInviteMethod('manual')}>
            {/* <Icon name='TerminalSquare' className='w-4 h-4 mr-2' /> */}
            Invite Manually
          </TabsTrigger>
          <TabsTrigger value="csv" onClick={() => setInviteMethod('csv')}>
            {/* <Icon name='Code2' className='w-4 h-4 mr-2' /> */}
            Upload CSV
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};

export const InviteCandidates = ({ files, setFiles, fields, setFields, inviteMethod, limit }) => {
  const context = useInterviewForm();
  const form = (context as any)?.inviteForm;

  const { invite } = form.getValues();

  const handleFileUpload = (event) => {
    const file = event.target.files;

    if (file && file.length > 0) {
      onDrop(file); // Pass the files array directly to `onDrop`
    }
  };
  const onDrop = useCallback(async (file: File[]) => {
    const isValid = validateFileUploaded({
      file: file[0],
      acceptedTypes: [],
      sizeLimit: 3,
    });
    if (file.length > 0 && isValid) {
      setFiles(Object.values(file));
      getFileData(Object.values(file));
      return;
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'application/vnd.ms-excel': ['.xls', '.xlsx'],
      'text/csv': ['.csv'],
    },
    onDrop,
  });

  const getFileData = (file) => {
    getUploadedFileData(file[0], (data) => {
      const [headers, ...rows] = data as any;
      const formattedHeader = headers?.map((header) => header.toLowerCase());
      const nameIndex = formattedHeader?.indexOf('name');
      const emailIndex = formattedHeader?.indexOf('email');

      if (nameIndex === -1 || emailIndex === -1) {
        setFiles(null);
        setFields([]);
        form.setValue('invite', []);
        toast.error(`Please provide a valid template`);
        return;
      }

      // Extracting Name and Email from each row
      const filteredData = data
        ?.filter((entry) => {
          const name = entry[nameIndex]?.trim()?.toLowerCase();
          const email = entry[emailIndex]?.trim()?.toLowerCase();
          if (name === 'example' && email === '<EMAIL>') {
            return false;
          }
          return true;
        })
        .map((entry) => ({
          name: entry[nameIndex]?.trim(),
          email: entry[emailIndex]?.trim().toLowerCase(),
        }));

      const uniqueData = filterValues(filteredData, 'email', emailPattern);
      setFields(uniqueData);
      setFiles(file);
      form.setValue('invite', uniqueData);
    });
  };

  return (
    <div className="relative">
      <Form {...(form as any)} className="h-full w-full">
        <div className="w-full">
          {inviteMethod === 'manual' ? (
            <div className="h-80">
              <CandidateList
                fields={fields}
                disableAdd={!!files}
                setFields={setFields}
                invite={invite}
                form={form}
                limit={limit}
                files={files}
              />
            </div>
          ) : (
            <div>
              <div className="flex items-center justify-between">
                <p>{files ? 'Uploaded File' : 'Upload Excel / CSV File'}</p>
                <Button
                  variant="link"
                  type="button"
                  onClick={() =>
                    downloadTemplate({
                      data: bulkUploadTemplate,
                      excelSheetName: 'Bulk_Upload_Template',
                      sheetName: 'Invite_Template',
                      columnWidth: 24,
                    })
                  }
                  className="p-0"
                >
                  <Icon name={'Download'} className="mr-2 h-4 w-4" />
                  Download Template
                </Button>
              </div>

              <div
                className="mt-4 flex cursor-pointer flex-col items-center justify-center rounded-md border border-dashed border-gray-300 py-8"
                {...getRootProps()}
              >
                {files ? (
                  <div
                    className="z-40 flex items-center justify-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      setFiles(null);
                      setFields([]);
                      form?.setValue('invite', []);
                    }}
                  >
                    <span>{files[0]?.name}</span>
                    <Icon name="Trash2" className="h-4 w-4 text-destructive" />
                  </div>
                ) : (
                  <div className="">
                    <Icon name="Upload" className="h-8 w-8 opacity-80" />
                    <div className="flex w-full flex-col items-center justify-center pb-4 pt-3">
                      <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                        <span className="font-semibold">Click to upload</span>
                        &nbsp; or <span className="font-semibold">Drag and Drop</span>
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Format accepted : XLS, XSLX, CSV
                      </p>
                    </div>
                    <Input
                      {...getInputProps()}
                      id="dropzone-file"
                      type="file"
                      disabled={!!files}
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                  </div>
                )}
              </div>

              {files ? (
                <div className="h-[174px] pt-4">
                  <CandidateList
                    fields={fields}
                    setFields={setFields}
                    showAddButton={false}
                    invite={invite}
                    form={form}
                    limit={limit}
                    files={files}
                  />
                </div>
              ) : (
                <p className={`absolute -bottom-10 text-sm opacity-60`}>
                  Maximum Number of Candidates should be {limit}
                </p>
              )}
            </div>
          )}
        </div>
      </Form>
    </div>
  );
};
