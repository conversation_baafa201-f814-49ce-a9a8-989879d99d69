import { useEffect, useRef, useState } from 'react';

import { hireMembersColumns, membersColumns } from '@/components/data-table/bulk-invites/columns';
import { DataTable } from '@/components/data-table/bulk-invites/data-table';
import { Icon } from '@/icons';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@camped-ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';

export const MemberAccessHeader = ({ isInterviewthon }) => {
  return (
    <div className="flex items-start justify-between">
      <h1 className="text-xl font-semibold">
        {isInterviewthon ? 'Group Access' : 'Member Access'}
      </h1>
    </div>
  );
};

export const MemberAccess = ({ fields, setFields, members, group, isInterviewathon }) => {
  const [openPopover, setOpenPopover] = useState(false);
  const [selectedValue, setSelectedValue]: any = useState({});

  const buttonRef = useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = useState(0);
  useEffect(() => {
    if (buttonRef.current) {
      const width = buttonRef.current.getBoundingClientRect().width;
      setButtonWidth(width);
    }
  }, []);
  const handleDeleteField = (email) => {
    const newFields = [...fields];
    const updatedFields = newFields?.filter((item) => item?.user?.email !== email);
    setFields(updatedFields);
  };
  const handleAddField = async (data) => {
    if (isInterviewathon) {
      const selectedGroup = group?.group?.find((item) => item?.id === selectedValue?.id);
      const member = selectedGroup?.GroupMembershipMapping?.map((item) => {
        return {
          ...item?.membership,
          departmentRole: 'MEMBER',
          groupRole: item?.role,
          department: selectedGroup?.department,
          group: selectedValue,
        };
      });
      const admin = selectedGroup?.department?.DepartmentMembershipMapping?.map((item) => ({
        ...item?.membership,
        departmentRole: 'ADMIN',
        group: selectedValue,
      }));
      const allItems = [...fields, ...(member || []), ...(admin || [])];
      const uniqueMap = new Map(allItems.map((item) => [item?.id, item]));
      const unique = Array.from(uniqueMap.values());
      setFields(unique);
    } else {
      const emailPresent = fields?.find((item) => item?.user?.email === data?.user?.email);
      if (emailPresent) {
        toast.error('Email already added!');
      } else {
        setFields([...fields, selectedValue]);
      }
    }
    setSelectedValue({});
  };
  const optionValues = isInterviewathon ? group?.group : members?.items;
  const placeholder = isInterviewathon ? 'Select Group' : 'Select member';
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <Popover open={openPopover} onOpenChange={() => setOpenPopover(!openPopover)}>
          <PopoverContent style={{ width: buttonWidth }} className="p-0">
            <Command>
              <CommandInput placeholder={'Search by email'} />
              <CommandList>
                <CommandEmpty>No results found.</CommandEmpty>
                <CommandGroup>
                  {optionValues?.map((option) => {
                    const data = fields?.find((item) => item?.userId === option?.userId);
                    if (data) return null;
                    return (
                      <CommandItem
                        key={option?.user?.email ?? option?.id}
                        onSelect={() => {
                          setSelectedValue(option);
                          setOpenPopover(false);
                        }}
                      >
                        <span>{option?.user?.email ?? option?.name}</span>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button
              onClick={() => setOpenPopover(!openPopover)}
              className="flex w-full max-w-[400px] items-center justify-between rounded-md border bg-gray-50 bg-secondary px-4 py-2 text-gray-900 transition-all duration-75 focus:outline-none sm:text-sm"
              ref={buttonRef}
            >
              <p
                className={` ${
                  !!selectedValue?.user?.email || !!selectedValue?.name ? '' : 'opacity-40'
                } `}
              >
                {selectedValue?.user?.email ?? selectedValue?.name ?? placeholder}
              </p>
            </button>
          </PopoverTrigger>
        </Popover>
        <Button
          className=""
          size="sm"
          disabled={!selectedValue?.user && !selectedValue?.id}
          onClick={() => handleAddField(selectedValue)}
        >
          <Icon name={'Plus'} className="mr-2 h-4 w-4" />
          Add
        </Button>
      </div>
      {fields?.length > 0 ? (
        <DataTable
          height={'40vh'}
          data={fields?.map((item) => ({ ...item, email: item?.user?.email }))}
          columns={isInterviewathon ? membersColumns : hireMembersColumns}
          handleDelete={(email) => handleDeleteField(email)}
          disableButtons={!fields?.length}
        />
      ) : null}
    </div>
  );
};
