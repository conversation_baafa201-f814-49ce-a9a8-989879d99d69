'use client';

import { ProfileComponent } from '../../profileComponent';
import { DataTableColumnHeader } from '../data-table-column-header';

export const membersColumnList = (isInstitution) => [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" className="hidden" />
    ),
    cell: ({ row }) => <p className="hidden w-[100px]">{row.getValue('id')}</p>,
    enableHiding: false,
  },
  {
    accessorKey: 'user',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => <ProfileComponent user={row.getValue('user')} />,
    enableHiding: false,
  },
  {
    accessorKey: 'role',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Role" />,
    cell: ({ row }) => <div className="w-[60px]">{row.getValue('role')}</div>,
  },
  {
    accessorKey: 'delete',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Delete" />,
    enableSorting: false,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" className="hidden" />
    ),
    cell: ({ row }) => <p className="hidden w-[100px]">{row.getValue('email')}</p>,
    enableHiding: false,
  },
];
export const groupMembersColumnList = (isInstitution) => [
  {
    accessorKey: 'id',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" className="hidden" />
    ),
    cell: ({ row }) => <p className="hidden w-[100px]">{row.getValue('id')}</p>,
    enableHiding: false,
  },
  {
    accessorKey: 'user',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => <ProfileComponent user={row.getValue('user')} />,
    enableHiding: false,
  },
  {
    accessorKey: 'role',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Role" />,
    cell: ({ row }) => <div className="w-[60px]">{row.getValue('role')}</div>,
  },
  {
    accessorKey: 'delete',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Delete" />,
    enableSorting: false,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" className="hidden" />
    ),
    cell: ({ row }) => <p className="hidden w-[100px]">{row.getValue('email')}</p>,
    enableHiding: false,
  },
];
