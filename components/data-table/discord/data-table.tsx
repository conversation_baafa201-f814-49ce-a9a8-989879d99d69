'use client';

import * as React from 'react';

import { useRouter } from 'next/navigation';

import { AddDiscordWebhookModal } from '@/components/Modal/add-discord-webhook';
import { metaData } from '@/constants/integration-platform';
import { useDataTable } from '@/hooks/client/use-data-table';
import { Icon } from '@/icons';
import { DataTable } from '@/packages/shared-data-table/data-table';
import { DataTableToolbar } from '@/packages/shared-data-table/data-table-toolbar';
import { createDiscordWebhook, deleteWebhooks, updateDiscordWebhook } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';

import { membersColumnList } from './columns';

interface TasksTableProps {
  tasksPromise: any;
}

export function DiscordWebhookTable({ tasksPromise }: TasksTableProps) {
  const { data, pageCount } = tasksPromise;
  const [showModal, setShowModal]: any = React.useState(null);
  const [update, setUpdate]: any = React.useState(null);
  const router = useRouter();

  const tenantId = getCookie('aceprepTenantId');
  const [isLoading, setIsLoading]: any = React.useState(null);

  const handleDelete = async (id) => {
    toast.loading('Deleting webhook...');
    const webhook = await deleteWebhooks({ id });
    toast.remove();
    if (webhook?.id) {
      toast.success('Webhook deleted successfully');
      router.refresh();
    } else {
      toast.error('error deleting Webhook');
    }
  };

  const { table } = useDataTable({
    data,
    columns: membersColumnList(handleDelete, setShowModal, setUpdate),
    pageCount,
    filterFields: [],
    enableAdvancedFilter: false,
  });
  const handleSubmit = async ({ url, name }) => {
    setIsLoading('add');
    let webhook;
    if (update) {
      webhook = await updateDiscordWebhook({
        id: update?.id,
        data: { url, name, organizationId: tenantId },
      });
    } else {
      webhook = await createDiscordWebhook({ data: { url, name, organizationId: tenantId } });
    }
    if (webhook?.webhook?.id) {
      toast.success(`Webhook ${update ? 'updated' : 'added'} successfully`);
      setUpdate(null);
      setShowModal(null);
      router.refresh();
    } else {
      toast.error(`Failed to ${update ? 'update' : 'add'}  webhook`);
    }
    setIsLoading(null);
    return webhook;
  };

  return (
    <DataTable table={table}>
      <DataTableToolbar table={table} hasBack filterFields={[]} title={'Discord'}>
        <Button size="sm" onClick={() => setShowModal('add')}>
          Add Discord Webhook
        </Button>
      </DataTableToolbar>
      <AddDiscordWebhookModal
        showModal={showModal}
        setShowModal={setShowModal}
        update={update}
        setUpdate={setUpdate}
        handleSubmit={handleSubmit}
        setIsLoading={setIsLoading}
        isLoading={isLoading}
        item={metaData?.find((item) => item?.platform?.toLowerCase() === 'discord')}
      />
    </DataTable>
  );
}
