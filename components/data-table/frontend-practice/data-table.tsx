'use client';

import * as React from 'react';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

import { BreadCrumbWrapper } from '@/components/breadcrumb/wrapper';
import { frontendChallenges } from '@/constants/apps-metadata';
import { useDataTable } from '@/hooks/client/use-data-table';
import { Icon } from '@/icons';
import { DataTablePagination } from '@/packages/shared-data-table//data-table-pagination';
import { type Table as TanstackTable, flexRender } from '@tanstack/react-table';

import { Button } from '@camped-ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@camped-ui/table';

import { problemsColumnList } from './columns';
import { DataTableToolbar } from './data-table-toolbar';

interface TasksTableProps {
  tasksPromise: any;
  userId: any;
}

export function FrontEndCodingTable({ tasksPromise, userId }: TasksTableProps) {
  // Feature flags for showcasing some additional features. Feel free to remove them.

  // Memoize the columns so they don't re-render on every render
  const columns = problemsColumnList;

  /**
   * This component can render either a faceted filter or a search filter based on the `options` prop.
   *
   * @prop options - An array of objects, each representing a filter option. If provided, a faceted filter is rendered. If not, a search filter is rendered.
   *
   * Each `option` object has the following properties:
   * @prop {string} label - The label for the filter option.
   * @prop {string} value - The value for the filter option.
   * @prop {React.ReactNode} [icon] - An optional icon to display next to the label.
   * @prop {boolean} [withCount] - An optional boolean to display the count of the filter option.
   */

  const difficulty = [
    {
      label: 'Easy',
      value: 'EASY',
      // icon: ArrowDownIcon,
    },
    {
      label: 'Medium',
      value: 'MEDIUM',
      // icon: ArrowRightIcon,
    },
    {
      label: 'Hard',
      value: 'HARD',
      // icon: ArrowUpIcon,
    },
  ];
  const filterFields: any = [
    {
      label: 'title',
      value: 'title',
      placeholder: 'Filter by title...',
    },
    {
      label: 'Difficulty',
      value: 'difficulty',
      options: difficulty,
    },
  ];
  const { table } = useDataTable({
    data: tasksPromise?.data.result,
    columns,
    pageCount: tasksPromise?.data.totalPages,
    filterFields,
    enableAdvancedFilter: false,
  });
  const getCommonPinningStyles = (column: any, index): React.CSSProperties => {
    const isPinned = column.getIsPinned();
    const isLastLeftPinnedColumn = isPinned === 'left' && column.getIsLastColumn('left');
    const isFirstRightPinnedColumn = isPinned === 'right' && column.getIsFirstColumn('right');

    return {
      boxShadow: isLastLeftPinnedColumn
        ? '-4px 0 4px -4px gray inset'
        : isFirstRightPinnedColumn
        ? '4px 0 4px -4px gray inset'
        : undefined,
      left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
      right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
      opacity: 1,
      position: isPinned ? 'sticky' : 'relative',

      zIndex: isPinned ? 1000 : 0,
      background: 'white',
    };
  };
  const searchParams = useSearchParams();
  const framework = searchParams?.get('framework');

  return (
    <div className="w-full space-y-2.5 overflow-auto">
      <BreadCrumbWrapper
        data={[
          {
            title: 'Frontend Problems',
          },
          {
            title: frontendChallenges?.find((item) => item?.key === framework)?.name ?? 'All',
          },
        ]}
      />
      <DataTableToolbar table={table} filterFields={filterFields} showView={true} />
      <div className="relative rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup: any) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  return (
                    <TableHead
                      key={header.id}
                      style={{
                        ...getCommonPinningStyles(header.column, index),
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => {
                if ((row.original as any)?.status === 'ARCHIVED' && !userId) {
                  return (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className="blur-effect relative items-center bg-gray-500"
                    >
                      {row.getVisibleCells().map((cell) => {
                        if (cell.column.id === 'solve') {
                          return (
                            <TableCell key={cell.id}>
                              <Button key={cell.id} disabled={true}>
                                Solve
                              </Button>
                            </TableCell>
                          );
                        }
                        return (
                          <TableCell key={cell.id}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        );
                      })}
                      <Link
                        href="/sign-in"
                        className="hover:[linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)), #0D2247] group absolute flex scale-100 items-center justify-center gap-x-2 rounded-full bg-primary px-4 py-2 text-[13px] font-semibold text-white no-underline transition-all duration-75 active:scale-95"
                        style={{
                          boxShadow:
                            '0px 1px 4px rgba(13, 34, 71, 0.17), inset 0px 0px 0px 1px bg-primary, inset 0px 0px 0px 2px rgba(255, 255, 255, 0.1)',
                          flex: 1,
                          left: '45%',
                          top: '40%',
                        }}
                      >
                        <Icon name="smallLock" className="h-4 w-4" />
                        <p> Sign in to unlock </p>
                      </Link>
                    </TableRow>
                  );
                }
                return (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell, index) => (
                      <TableCell
                        key={cell.id}
                        style={{
                          ...getCommonPinningStyles(cell.column, index),
                        }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={table.getAllColumns().length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex flex-col gap-2.5">
        <DataTablePagination table={table} />
        {table.getFilteredSelectedRowModel().rows.length > 0}
      </div>
    </div>
  );
}
