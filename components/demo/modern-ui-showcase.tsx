'use client';

import { ModernExecutiveSummary } from '@/components/section/modern-executive-summary';
import { ModernInterviewHero } from '@/components/section/modern-interview-hero';
import { ModernKeyMetrics } from '@/components/section/modern-key-metrics';

// Sample data for demonstration
const sampleCareerPractice = {
  id: 'demo-interview-123',
  event: 'Software Engineer Interview',
  role: 'Senior Software Engineer',
  level: 'Senior Level',
  candidateName: 'Shreeram Kodlekere',
  candidateEmail: '<EMAIL>',
  timing: {
    startTime: '2024-01-15T09:00:00Z',
    completedTime: '2024-01-15T10:30:00Z',
  },
  feedback: {
    overall_score: 8.5,
    overall_recommendation: 'Hire',
    technical_skills: {
      score: 9,
      feedback: 'Excellent problem-solving skills and clean code implementation',
    },
    communication_skills: {
      score: 8,
      feedback: 'Clear articulation of thoughts and good listening skills',
    },
    problem_solving: { score: 8.5, feedback: 'Strong analytical thinking and systematic approach' },
    coding_skills: 9,
    system_design: 7.5,
    strengths: [
      'Excellent technical knowledge in React and Node.js',
      'Strong problem-solving approach with clean, maintainable code',
      'Good communication skills and ability to explain complex concepts',
      'Experience with modern development practices and tools',
    ],
    areas_for_improvement: [
      'Could improve system design knowledge for large-scale applications',
      'More experience with microservices architecture would be beneficial',
      'Consider learning more about performance optimization techniques',
    ],
    candidate_legitimacy: {
      flag_level: 'minimal',
      reason: 'No suspicious activity detected during the interview',
    },
    key_highlights: [
      'Demonstrated strong full-stack development capabilities',
      'Excellent problem-solving methodology',
      'Good cultural fit with collaborative approach',
    ],
  },
};

const sampleUserProfile = {
  name: 'Shreeram Kodlekere',
  email: '<EMAIL>',
  profilePicture: null, // Will use fallback initials
};



export const ModernUIShowcase = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-8 dark:bg-gray-900">
      <div className="mx-auto max-w-7xl space-y-8">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold text-gray-900 dark:text-white">
            Modern Interview UI Showcase
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            A modern, interviewer-focused design for better decision making
          </p>
        </div>

        {/* Modern Hero Section */}
        <div>
          <h2 className="mb-4 text-2xl font-semibold text-gray-800 dark:text-gray-200">
            1. Hero Section - Key Information at a Glance
          </h2>
          <ModernInterviewHero
            careerPractice={sampleCareerPractice}
            userProfile={sampleUserProfile}
          />
        </div>

        {/* Modern Key Metrics */}
        <div>
          <h2 className="mb-4 text-2xl font-semibold text-gray-800 dark:text-gray-200">
            2. Key Performance Metrics Dashboard
          </h2>
          <ModernKeyMetrics careerPractice={sampleCareerPractice} />
        </div>

        {/* Modern Executive Summary */}
        <div>
          <h2 className="mb-4 text-2xl font-semibold text-gray-800 dark:text-gray-200">
            3. Executive Summary - Quick Decision Support
          </h2>
          <ModernExecutiveSummary careerPractice={sampleCareerPractice} />
        </div>

        {/* Comparison Note */}
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-6 dark:border-blue-800 dark:bg-blue-950/20">
          <h3 className="mb-3 text-lg font-semibold text-blue-900 dark:text-blue-100">
            🎯 Key Improvements in the Modern Design
          </h3>
          <ul className="space-y-2 text-blue-800 dark:text-blue-200">
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Immediate Decision Support:</strong> Hire/Don't Hire recommendation
                prominently displayed
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Visual Hierarchy:</strong> Most important information (candidate, score,
                recommendation) at the top
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Scannable Metrics:</strong> Key performance indicators in an easy-to-digest
                dashboard format
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Executive Summary:</strong> Strengths, improvements, and highlights in
                organized cards
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Modern Aesthetics:</strong> Clean design with proper spacing, colors, and
                typography
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="font-bold text-green-600">✓</span>
              <span>
                <strong>Progressive Disclosure:</strong> Detailed analysis moved to expandable
                sections
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
