'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Award,
  Brain,
  Clock,
  Code,
  FileText,
  Lightbulb,
  MessageSquare,
  Shield,
  Target,
  TrendingUp,
  Users,
  Video,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

import { DetailScoreSection } from './detail-score-section';
import { EditableDecisionCard } from './editable-decision-card';
import { ResumeSection } from './resume-section';

interface ModernDetailedAnalysisProps {
  careerPractice: any;
  userProfile?: any;
  questions?: any[];
  userId?: string;
  reason?: string;
  flag?: string;
  setFlag?: (flag: string) => void;
  setReason?: (reason: string) => void;
  handleOnSave?: () => void;
}

// Modern Card Components for Enhanced Feedback Display
const ModernFeedbackCard = ({ title, icon: Icon, color, children, badge }) => (
  <Card className="border-0 bg-gradient-to-br from-white to-gray-50/50 shadow-sm dark:from-gray-900 dark:to-gray-800/50">
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={cn('rounded-xl p-3', color)}>
            <Icon className="h-6 w-6" />
          </div>
          <CardTitle className="text-xl font-semibold">{title}</CardTitle>
        </div>
        {badge && (
          <Badge variant="secondary" className="text-xs font-medium">
            {badge}
          </Badge>
        )}
      </div>
    </CardHeader>
    <CardContent className="pt-0">{children}</CardContent>
  </Card>
);

const StrengthsImprovementsGrid = ({ strengths, improvements }) => (
  <div className="grid gap-6 md:grid-cols-2">
    {strengths && (
      <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-900/20">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <CardTitle className="text-lg text-green-800 dark:text-green-200">
              Key Strengths
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm leading-relaxed text-green-700 dark:text-green-300">
            {typeof strengths === 'string'
              ? strengths
              : strengths?.feedback || 'No strengths data available'}
          </p>
        </CardContent>
      </Card>
    )}

    {improvements && (
      <Card className="border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-900/20">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-orange-600" />
            <CardTitle className="text-lg text-orange-800 dark:text-orange-200">
              Areas for Improvement
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm leading-relaxed text-orange-700 dark:text-orange-300">
            {typeof improvements === 'string'
              ? improvements
              : improvements?.feedback || 'No improvement areas identified'}
          </p>
        </CardContent>
      </Card>
    )}
  </div>
);

export const ModernDetailedAnalysis = ({
  careerPractice,
  userProfile,
  userId,
  reason,
  flag,
  setFlag,
  setReason,
  handleOnSave,
}: ModernDetailedAnalysisProps) => {
  const getSectionIcon = (section: string) => {
    const icons = {
      resume: FileText,
      timing: Clock,
      proctoring: Shield,
      recommendations: Target,
      coreValues: Users,
      jobFit: Award,
      feedback: MessageSquare,
      comments: MessageSquare,
      recordings: Video,
      questions: Code,
      strengths: TrendingUp,
      improvements: Lightbulb,
    };
    const Icon = icons[section] || FileText;
    return <Icon className="h-5 w-5" />;
  };

  const getSectionColor = (section: string) => {
    const colors = {
      resume: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      timing: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      proctoring: 'text-red-600 bg-red-100 dark:bg-red-900/20',
      recommendations: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
      coreValues: 'text-indigo-600 bg-indigo-100 dark:bg-indigo-900/20',
      jobFit: 'text-teal-600 bg-teal-100 dark:bg-teal-900/20',
      feedback: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      comments: 'text-gray-600 bg-gray-100 dark:bg-gray-900/20',
      recordings: 'text-pink-600 bg-pink-100 dark:bg-pink-900/20',
      questions: 'text-cyan-600 bg-cyan-100 dark:bg-cyan-900/20',
      strengths: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      improvements: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
    };
    return colors[section] || 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  };

  const sections = [
    // Executive Summary Section - Always show first if we have strengths and improvements
    {
      key: 'strengths',
      title: 'Executive Summary',
      condition:
        careerPractice?.feedback?.strengths || careerPractice?.feedback?.areas_for_improvement,
      content: (
        <div className="space-y-4">
          <div className="mb-4 flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Key insights and recommendations from the interview assessment
            </p>
          </div>
          <StrengthsImprovementsGrid
            strengths={careerPractice?.feedback?.strengths}
            improvements={careerPractice?.feedback?.areas_for_improvement}
          />
        </div>
      ),
    },

    {
      key: 'resume',
      title: 'Resume Analysis',
      condition: careerPractice?.feedback?.resume,
      content: (
        <ResumeSection
          feedback={careerPractice?.feedback?.resume}
          resumeUrl={userProfile?.resumeUrl}
        />
      ),
    },

    {
      key: 'proctoring',
      title: 'Proctoring Feedback',
      condition: careerPractice?.feedback?.candidate_legitimacy?.flag_level,
      content: (
        <ModernFeedbackCard
          title="Proctoring Risk Assessment"
          icon={Shield}
          color="text-red-600 bg-red-100 dark:bg-red-900/20"
          badge={careerPractice?.feedback?.candidate_legitimacy?.flag_level}
        >
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Risk Level:
              </span>
              <Badge
                variant={
                  careerPractice?.feedback?.candidate_legitimacy?.flag_level?.toLowerCase() ===
                  'minimal'
                    ? 'default'
                    : careerPractice?.feedback?.candidate_legitimacy?.flag_level?.toLowerCase() ===
                      'medium'
                    ? 'secondary'
                    : 'destructive'
                }
              >
                {careerPractice?.feedback?.candidate_legitimacy?.flag_level}
              </Badge>
            </div>
            <p className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">
              {careerPractice?.feedback?.candidate_legitimacy?.reason}
            </p>
          </div>
        </ModernFeedbackCard>
      ),
    },

    {
      key: 'recommendations',
      title: 'Recommendation',
      condition: careerPractice?.feedback?.overall_recommendation,
      content: (
        <ModernFeedbackCard
          title="Hiring Recommendation"
          icon={Target}
          color="text-purple-600 bg-purple-100 dark:bg-purple-900/20"
          badge={flag || careerPractice?.feedback?.overall_recommendation?.decision}
        >
          <EditableDecisionCard
            title=""
            reason={reason ?? careerPractice?.feedback?.overall_recommendation}
            flag={flag}
            setFlag={setFlag}
            setReason={setReason}
            onSave={handleOnSave}
            data={[
              { name: 'Hire', value: 'Hire' },
              { name: 'Weak hire', value: 'Weak hire' },
              { name: 'No hire', value: 'No hire' },
            ]}
          />
        </ModernFeedbackCard>
      ),
    },

    {
      key: 'jobFit',
      title: 'Job Fit Analysis',
      condition: careerPractice?.feedback?.job_fit,
      content: (
        <ModernFeedbackCard
          title="Job Fit Analysis"
          icon={Award}
          color="text-teal-600 bg-teal-100 dark:bg-teal-900/20"
          badge={`${careerPractice?.feedback?.job_fit?.Overall_score?.score || 'N/A'}/10`}
        >
          <DetailScoreSection data={careerPractice?.feedback?.job_fit} title="" user={userId} />
        </ModernFeedbackCard>
      ),
    },

    {
      key: 'coreValues',
      title: 'Core Values',
      condition: careerPractice?.feedback?.coreValue,
      content: (
        <ModernFeedbackCard
          title="Core Values Assessment"
          icon={Users}
          color="text-indigo-600 bg-indigo-100 dark:bg-indigo-900/20"
          badge={careerPractice?.feedback?.coreValue?.Overall_fit?.decision || 'N/A'}
        >
          <DetailScoreSection data={careerPractice?.feedback?.coreValue} title="" user={userId} />
        </ModernFeedbackCard>
      ),
    },

    {
      key: 'comments',
      title: 'Interview Comments',
      condition: careerPractice?.comments?.[0]?.message,
      content: (
        <Card>
          <CardHeader className="p-3">
            <CardTitle className="text-lg capitalize">Comments</CardTitle>
          </CardHeader>
          <Separator />
          <CardContent className="p-3">
            <p className="text-gray-700 dark:text-gray-300">
              {careerPractice?.comments?.[0]?.message}
            </p>
          </CardContent>
        </Card>
      ),
    },
  ];

  const validSections = sections.filter((section) => section.condition);

  if (validSections.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Detailed Analysis Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Detailed analysis will be available once the interview feedback is generated.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {validSections.map((section) => (
        <div key={section.key} className="space-y-2">
          {/* Section Header */}
          <div className="mb-4 flex items-center gap-3">
            <div className={cn('rounded-lg p-2', getSectionColor(section.key))}>
              {getSectionIcon(section.key)}
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {section.title}
            </h2>
          </div>

          {/* Section Content */}
          <div className="pl-0">{section.content}</div>
        </div>
      ))}
    </div>
  );
};
