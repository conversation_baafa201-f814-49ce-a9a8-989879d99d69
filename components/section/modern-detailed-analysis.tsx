'use client';

import { useState } from 'react';

import {
  <PERSON>ert<PERSON><PERSON>gle,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Clock,
  Code,
  Eye,
  FileText,
  MessageSquare,
  Monitor,
  Shield,
  Video,
  XCircle,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@camped-ui/collapsible';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

import { DecisionCard } from './decision-card';
import { DetailScoreSection } from './detail-score-section';
import { EditableDecisionCard } from './editable-decision-card';
import { FeedBackSection } from './feedback-section';
import { ResumeSection } from './resume-section';

interface ModernDetailedAnalysisProps {
  careerPractice: any;
  userProfile?: any;
  questions?: any[];
  userId?: string;
  reason?: string;
  flag?: string;
  setFlag?: (flag: string) => void;
  setReason?: (reason: string) => void;
  handleOnSave?: () => void;
}

export const ModernDetailedAnalysis = ({
  careerPractice,
  userProfile,
  questions = [],
  userId,
  reason,
  flag,
  setFlag,
  setReason,
  handleOnSave,
}: ModernDetailedAnalysisProps) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    resume: false,
    timing: false,
    proctoring: false,
    recommendations: false,
    coreValues: false,
    jobFit: false,
    feedback: false,
    comments: false,
    recordings: false,
    questions: false,
  });

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const getSectionIcon = (section: string) => {
    const icons = {
      resume: FileText,
      timing: Clock,
      proctoring: Shield,
      recommendations: CheckCircle,
      coreValues: Eye,
      jobFit: CheckCircle,
      feedback: MessageSquare,
      comments: MessageSquare,
      recordings: Video,
      questions: Code,
    };
    const Icon = icons[section] || FileText;
    return <Icon className="h-5 w-5" />;
  };

  const getSectionColor = (section: string) => {
    const colors = {
      resume: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      timing: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      proctoring: 'text-red-600 bg-red-100 dark:bg-red-900/20',
      recommendations: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
      coreValues: 'text-indigo-600 bg-indigo-100 dark:bg-indigo-900/20',
      jobFit: 'text-teal-600 bg-teal-100 dark:bg-teal-900/20',
      feedback: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      comments: 'text-gray-600 bg-gray-100 dark:bg-gray-900/20',
      recordings: 'text-pink-600 bg-pink-100 dark:bg-pink-900/20',
      questions: 'text-cyan-600 bg-cyan-100 dark:bg-cyan-900/20',
    };
    return colors[section] || 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  };

  const sections = [
    {
      key: 'resume',
      title: 'Resume Analysis',
      condition: careerPractice?.feedback?.resume,
      content: (
        <ResumeSection
          feedback={careerPractice?.feedback?.resume}
          resumeUrl={userProfile?.resumeUrl}
        />
      ),
    },
    {
      key: 'timing',
      title: 'Interview Timing & Duration',
      condition: careerPractice?.timing?.completedTime,
      content: (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">
                {careerPractice?.isPlacement ? 'Interviewathon' : 'Interview'} Duration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Started:</span>
                  <span className="text-sm font-medium">
                    {careerPractice?.timing?.startTime
                      ? new Date(careerPractice.timing.startTime).toLocaleString()
                      : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Completed:</span>
                  <span className="text-sm font-medium">
                    {careerPractice?.timing?.completedTime
                      ? new Date(careerPractice.timing.completedTime).toLocaleString()
                      : 'N/A'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Proctoring Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {careerPractice?.proctorWarnings?.tabSwitch && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Tab Switches:</span>
                    <Badge variant="destructive" className="text-xs">
                      {careerPractice.proctorWarnings.tabSwitch}
                    </Badge>
                  </div>
                )}
                {careerPractice?.proctorWarnings?.fullScreen && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Full Screen Exits:</span>
                    <Badge variant="destructive" className="text-xs">
                      {careerPractice.proctorWarnings.fullScreen}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      ),
    },
    {
      key: 'proctoring',
      title: 'Proctoring Risk Assessment',
      condition: careerPractice?.feedback?.candidate_legitimacy?.flag_level,
      content: (
        <DecisionCard
          title="Proctoring Risk Level"
          reason={careerPractice?.feedback?.candidate_legitimacy?.reason}
          flag={careerPractice?.feedback?.candidate_legitimacy?.flag_level}
        />
      ),
    },
    {
      key: 'recommendations',
      title: 'Hiring Recommendation',
      condition: careerPractice?.feedback?.overall_recommendation,
      content: (
        <EditableDecisionCard
          title="Recommendation"
          reason={reason ?? careerPractice?.feedback?.overall_recommendation}
          flag={flag}
          setFlag={setFlag}
          setReason={setReason}
          onSave={handleOnSave}
          data={[
            { name: 'Hire', value: 'Hire' },
            { name: 'Weak hire', value: 'Weak hire' },
            { name: 'No hire', value: 'No hire' },
          ]}
        />
      ),
    },
    {
      key: 'coreValues',
      title: 'Core Values Assessment',
      condition: careerPractice?.feedback?.coreValue,
      content: (
        <DetailScoreSection
          data={careerPractice?.feedback?.coreValue}
          title="Core Values"
          user={userId}
        />
      ),
    },
    {
      key: 'jobFit',
      title: 'Job Fit Analysis',
      condition: careerPractice?.feedback?.job_fit,
      content: (
        <DetailScoreSection
          data={careerPractice?.feedback?.job_fit}
          title="Job Fit"
          user={userId}
        />
      ),
    },
    {
      key: 'feedback',
      title: 'Detailed Feedback',
      condition:
        careerPractice?.feedback &&
        (careerPractice.feedback.areas_for_improvement || careerPractice.feedback.strengths),
      content: (
        <div className="space-y-4">
          {Object.entries(careerPractice?.feedback ?? {})?.map(([key, value]: any) =>
            !['areas_for_improvement', 'strengths']?.includes(key.toLowerCase()) ? null : (
              <FeedBackSection
                key={key}
                short_summary={value}
                title={key?.replaceAll(/\_/g, ' ')}
              />
            ),
          )}
        </div>
      ),
    },
    {
      key: 'comments',
      title: 'Interview Comments',
      condition: careerPractice?.comments?.[0]?.message,
      content: (
        <Card>
          <CardHeader className="p-3">
            <CardTitle className="text-lg capitalize">Comments</CardTitle>
          </CardHeader>
          <Separator />
          <CardContent className="p-3">
            <p className="text-gray-700 dark:text-gray-300">
              {careerPractice?.comments?.[0]?.message}
            </p>
          </CardContent>
        </Card>
      ),
    },
    {
      key: 'recordings',
      title: 'Interview Recordings',
      condition:
        careerPractice?.videoRecordings?.screen?.length > 0 ||
        careerPractice?.videoRecordings?.webcam?.length > 0,
      content: (
        <div className="space-y-4">
          {careerPractice?.videoRecordings?.screen?.map((recording: any, index: number) => (
            <div key={`screen-${index}`} className="space-y-2">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                Screen Recording {index + 1}
              </h4>
              <video
                className="w-full rounded-md"
                src={recording}
                controls
                playsInline
                width="100%"
                height="300"
              />
            </div>
          ))}
          {careerPractice?.videoRecordings?.webcam?.map((recording: any, index: number) => (
            <div key={`webcam-${index}`} className="space-y-2">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                Webcam Recording {index + 1}
              </h4>
              <video
                className="w-full rounded-md"
                src={recording}
                controls
                playsInline
                width="100%"
                height="300"
              />
            </div>
          ))}
        </div>
      ),
    },
  ];

  const validSections = sections.filter((section) => section.condition);

  if (validSections.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Detailed Analysis Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Detailed analysis will be available once the interview feedback is generated.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {validSections.map((section) => (
        <Collapsible
          key={section.key}
          open={expandedSections[section.key]}
          onOpenChange={() => toggleSection(section.key)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="h-auto w-full justify-between p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div className="flex items-center gap-3">
                <div className={cn('rounded-lg p-2', getSectionColor(section.key))}>
                  {getSectionIcon(section.key)}
                </div>
                <span className="text-left font-medium">{section.title}</span>
              </div>
              {expandedSections[section.key] ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="px-4 pb-4">{section.content}</CollapsibleContent>
        </Collapsible>
      ))}
    </div>
  );
};
