'use client';

import {
  <PERSON>ert<PERSON>ir<PERSON>,
  ArrowRight,
  CheckCircle,
  Lightbulb,
  Star,
  Target,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

interface SummaryCardProps {
  title: string;
  content: string | string[] | object;
  icon: React.ReactNode;
  variant: 'positive' | 'negative' | 'neutral' | 'highlight';
}

const SummaryCard = ({ title, content, icon, variant }: SummaryCardProps) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'positive':
        return {
          border: 'border-green-200 dark:border-green-800',
          bg: 'bg-green-50 dark:bg-green-950/20',
          iconBg: 'bg-green-100 dark:bg-green-900/30',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800 dark:text-green-200',
        };
      case 'negative':
        return {
          border: 'border-orange-200 dark:border-orange-800',
          bg: 'bg-orange-50 dark:bg-orange-950/20',
          iconBg: 'bg-orange-100 dark:bg-orange-900/30',
          iconColor: 'text-orange-600',
          titleColor: 'text-orange-800 dark:text-orange-200',
        };
      case 'highlight':
        return {
          border: 'border-blue-200 dark:border-blue-800',
          bg: 'bg-blue-50 dark:bg-blue-950/20',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800 dark:text-blue-200',
        };
      default:
        return {
          border: 'border-gray-200 dark:border-gray-700',
          bg: 'bg-gray-50 dark:bg-gray-950/20',
          iconBg: 'bg-gray-100 dark:bg-gray-900/30',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-800 dark:text-gray-200',
        };
    }
  };

  const styles = getVariantStyles();

  const renderContent = () => {
    if (typeof content === 'string') {
      return <p className="leading-relaxed text-gray-700 dark:text-gray-300">{content}</p>;
    }

    if (Array.isArray(content)) {
      return (
        <ul className="space-y-2">
          {content.map((item: string, index: number) => (
            <li key={index} className="flex items-start gap-2 text-gray-700 dark:text-gray-300">
              <ArrowRight className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" />
              <span>{item}</span>
            </li>
          ))}
        </ul>
      );
    }

    if (typeof content === 'object' && content !== null) {
      return (
        <div className="space-y-2">
          {Object.entries(content).map(([key, value]) => (
            <div key={key} className="text-gray-700 dark:text-gray-300">
              <span className="font-medium capitalize">{key.replace(/_/g, ' ')}: </span>
              <span>{value as string}</span>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <Card className={cn('transition-all duration-200 hover:shadow-md', styles.border, styles.bg)}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <div className={cn('rounded-lg p-2', styles.iconBg)}>
            <div className={styles.iconColor}>{icon}</div>
          </div>
          <CardTitle className={cn('text-lg font-semibold', styles.titleColor)}>{title}</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="pt-0">{renderContent()}</CardContent>
    </Card>
  );
};

interface ModernExecutiveSummaryProps {
  careerPractice: any;
}

export const ModernExecutiveSummary = ({ careerPractice }: ModernExecutiveSummaryProps) => {
  const feedback = careerPractice?.feedback || {};

  // Extract key information
  const strengths = feedback?.strengths;
  const areasForImprovement = feedback?.areas_for_improvement || feedback?.areas_of_improvement;
  const recommendation = feedback?.overall_recommendation;
  const keyHighlights = feedback?.key_highlights || feedback?.summary;

  // Generate recommendation reasoning
  const getRecommendationReasoning = () => {
    if (!recommendation) return null;

    const score = feedback?.overall_score || 0;
    const recommendationStr =
      typeof recommendation === 'string' ? recommendation : String(recommendation);
    const isPositive = ['hire', 'strong hire', 'weak hire'].includes(
      recommendationStr.toLowerCase(),
    );

    if (isPositive) {
      return `Based on an overall score of ${score}/10, the candidate demonstrates strong capabilities and is recommended for hiring. Key factors include solid technical skills, good communication, and problem-solving abilities.`;
    } else {
      return `With an overall score of ${score}/10, the candidate shows some gaps that need to be addressed. Consider additional evaluation or training before making a final decision.`;
    }
  };

  // Generate key highlights if not provided
  const getKeyHighlights = () => {
    if (keyHighlights) return keyHighlights;

    const highlights: string[] = [];
    const score = feedback?.overall_score || 0;

    if (score >= 8) {
      highlights.push('Exceptional performance across multiple areas');
    } else if (score >= 6) {
      highlights.push('Solid performance with room for growth');
    } else {
      highlights.push('Performance below expectations');
    }

    if (feedback?.technical_skills?.score >= 7 || feedback?.technical_skills >= 7) {
      highlights.push('Strong technical competency demonstrated');
    }

    if (feedback?.communication_skills?.score >= 7 || feedback?.communication >= 7) {
      highlights.push('Excellent communication and articulation');
    }

    return highlights.length > 0 ? highlights : ['Assessment completed successfully'];
  };

  const summaryCards = [
    {
      title: 'Key Strengths',
      content: strengths,
      icon: <TrendingUp className="h-5 w-5" />,
      variant: 'positive' as const,
    },
    {
      title: 'Areas for Improvement',
      content: areasForImprovement,
      icon: <TrendingDown className="h-5 w-5" />,
      variant: 'negative' as const,
    },
    {
      title: 'Key Highlights',
      content: getKeyHighlights(),
      icon: <Star className="h-5 w-5" />,
      variant: 'highlight' as const,
    },
    {
      title: 'Recommendation Reasoning',
      content: getRecommendationReasoning(),
      icon: <Target className="h-5 w-5" />,
      variant: 'neutral' as const,
    },
  ].filter((card) => card.content); // Only show cards with content

  if (summaryCards.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <AlertCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Summary Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Executive summary will be available once the interview feedback is generated.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="rounded-lg bg-purple-100 p-2 dark:bg-purple-900/20">
          <Lightbulb className="h-6 w-6 text-purple-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Executive Summary</h2>
          <p className="text-gray-600 dark:text-gray-300">
            Key insights and recommendations from the interview assessment
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {summaryCards.map((card, index) => (
          <SummaryCard
            key={index}
            title={card.title}
            content={card.content}
            icon={card.icon}
            variant={card.variant}
          />
        ))}
      </div>

      {/* Overall assessment badge */}
      {feedback?.overall_score && (
        <div className="flex justify-center">
          <Badge
            variant="outline"
            className={cn(
              'px-6 py-3 text-lg font-semibold',
              feedback.overall_score >= 8
                ? 'border-green-300 bg-green-50 text-green-800 dark:border-green-700 dark:bg-green-950/20 dark:text-green-200'
                : feedback.overall_score >= 6
                ? 'border-yellow-300 bg-yellow-50 text-yellow-800 dark:border-yellow-700 dark:bg-yellow-950/20 dark:text-yellow-200'
                : 'border-red-300 bg-red-50 text-red-800 dark:border-red-700 dark:bg-red-950/20 dark:text-red-200',
            )}
          >
            <CheckCircle className="mr-2 h-5 w-5" />
            Overall Assessment: {feedback.overall_score}/10
          </Badge>
        </div>
      )}
    </div>
  );
};
