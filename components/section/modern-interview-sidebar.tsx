'use client';

import { format } from 'date-fns';
import { 
  Calendar, 
  Clock, 
  Users, 
  Video, 
  MessageSquare, 
  FileText,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Send,
  Plus
} from 'lucide-react';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Textarea } from '@camped-ui/textarea';
import { cn } from '@camped-ui/lib';

interface ModernInterviewSidebarProps {
  careerPractice: any;
  userProfile?: any;
  user?: any;
  members?: any;
  comment?: any[];
  newComment?: string;
  setNewComment?: (value: string) => void;
  handleAddComment?: () => void;
  handleResend?: (data: any) => void;
  handleReject?: (data: any) => void;
  handleGenerateFeedback?: (id: string) => void;
  setShowReScheduleModal?: (show: boolean) => void;
  router?: any;
  isPublic?: boolean;
  loading?: boolean;
  isGenerating?: boolean;
  disableFeedback?: boolean;
  isLoading?: boolean;
  platform?: string;
  timeAgo?: (date: string) => string;
}

export const ModernInterviewSidebar = ({
  careerPractice,
  userProfile,
  user,
  members,
  comment = [],
  newComment = '',
  setNewComment,
  handleAddComment,
  handleResend,
  handleReject,
  handleGenerateFeedback,
  setShowReScheduleModal,
  router,
  isPublic = false,
  loading = false,
  isGenerating = false,
  disableFeedback = false,
  isLoading = false,
  platform,
  timeAgo
}: ModernInterviewSidebarProps) => {
  
  // Get interviewers from VideoCallInterview
  const interviewers = careerPractice?.VideoCallInterview?.[0]?.interviewers || [];

  return (
    <div className="flex w-full max-w-[370px] flex-col gap-6">
      {/* Quick Actions */}
      {!isPublic && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <div className="p-1 bg-blue-100 dark:bg-blue-900/20 rounded">
                <CheckCircle className="w-4 h-4 text-blue-600" />
              </div>
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {!careerPractice?.timing?.inviteTime && !careerPractice?.isPlacement && (
              <Button
                className="w-full"
                onClick={() => {
                  handleResend?.({ id: careerPractice?.id, isInvite: true });
                }}
              >
                <Send className="w-4 h-4 mr-2" />
                Invite Candidate
              </Button>
            )}

            {!careerPractice?.timing?.feedBackGenerateTime && (
              <Button
                className="w-full"
                variant="outline"
                onClick={() => {
                  handleGenerateFeedback?.(careerPractice?.id);
                }}
                disabled={isGenerating || careerPractice?.timing?.feedBackGenerateTime}
              >
                <Plus className="w-4 h-4 mr-2" />
                {isGenerating ? 'Generating...' : 'Generate Feedback'}
              </Button>
            )}

            {platform === 'hire' && (
              <Button
                className="w-full"
                variant="destructive"
                disabled={disableFeedback || isLoading}
                onClick={() => handleReject?.({ id: careerPractice?.id, withLink: false })}
              >
                <XCircle className="w-4 h-4 mr-2" />
                {careerPractice?.comments?.[0]?.status ?? 'Send Feedback'}
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Interview Details */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <div className="p-1 bg-purple-100 dark:bg-purple-900/20 rounded">
              <FileText className="w-4 h-4 text-purple-600" />
            </div>
            Interview Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Event:</span>
              <span className="text-sm text-gray-900 dark:text-gray-100">{careerPractice?.event}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Role:</span>
              <span className="text-sm text-gray-900 dark:text-gray-100">{careerPractice?.role}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Level:</span>
              <span className="text-sm text-gray-900 dark:text-gray-100">{careerPractice?.level}</span>
            </div>
            {careerPractice?.isPlacement && (
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Type:</span>
                <Badge variant="secondary" className="text-xs">Interviewathon</Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Video Call Interview Details */}
      {careerPractice?.VideoCallInterview?.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <div className="p-1 bg-green-100 dark:bg-green-900/20 rounded">
                <Video className="w-4 h-4 text-green-600" />
              </div>
              Video Interview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Meeting Type:</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">
                  {careerPractice.VideoCallInterview[0]?.meetingType}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Scheduled:</span>
                <span className="text-sm text-gray-900 dark:text-gray-100">
                  {format(
                    new Date(careerPractice.VideoCallInterview[0]?.scheduleTime),
                    'MMM dd, HH:mm'
                  )}
                </span>
              </div>

              {interviewers?.length > 0 && (
                <div>
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 block">Interviewers:</span>
                  <div className="space-y-1">
                    {interviewers.map((interviewer: any, index: number) => (
                      <div key={interviewer.userId} className="flex items-center gap-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">
                            {(interviewer?.user?.userProfile?.fullName || interviewer?.user?.email || 'U')[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {interviewer?.user?.userProfile?.fullName || interviewer?.user?.email}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="pt-2">
              {careerPractice?.VideoCallInterview[0]?.meetingStatus === 'COMPLETED' &&
              careerPractice?.VideoCallInterview[0]?.s3RecordingId ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    router?.push(`/video-interviews/${careerPractice?.VideoCallInterview[0]?.id}`);
                  }}
                >
                  <Video className="w-4 h-4 mr-2" />
                  View Recording
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    setShowReScheduleModal?.(true);
                  }}
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Reschedule
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Proctoring Warnings */}
      {(careerPractice?.proctorWarnings?.tabSwitch || careerPractice?.proctorWarnings?.fullScreen) && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2 text-orange-800 dark:text-orange-200">
              <AlertTriangle className="w-4 h-4" />
              Proctoring Alerts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {careerPractice.proctorWarnings.tabSwitch && (
              <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>Tab switching detected: {careerPractice.proctorWarnings.tabSwitch} times</span>
              </div>
            )}
            {careerPractice.proctorWarnings.fullScreen && (
              <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>Full screen exits: {careerPractice.proctorWarnings.fullScreen} times</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Notes Section */}
      {!careerPractice?.isPlacement && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <div className="p-1 bg-yellow-100 dark:bg-yellow-900/20 rounded">
                <MessageSquare className="w-4 h-4 text-yellow-600" />
              </div>
              Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 h-[200px] overflow-y-auto space-y-2">
              {comment.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-8">
                  No notes yet. Add the first note below.
                </p>
              ) : (
                comment.map((note, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                        {note?.commentBy?.userProfile?.fullName || note?.commentBy?.email}
                      </span>
                      <span className="text-xs text-gray-500">
                        {timeAgo?.(note?.date)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{note?.comment}</p>
                  </div>
                ))
              )}
            </div>
            
            <div className="space-y-2">
              <Textarea
                placeholder="Add a note..."
                value={newComment}
                onChange={(e) => setNewComment?.(e.target.value)}
                className="min-h-[80px] resize-none"
              />
              <Button
                size="sm"
                className="w-full"
                disabled={loading || !newComment.trim()}
                onClick={handleAddComment}
              >
                <Send className="w-4 h-4 mr-2" />
                Add Note
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Candidate Profile Summary */}
      {userProfile && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <div className="p-1 bg-indigo-100 dark:bg-indigo-900/20 rounded">
                <Users className="w-4 h-4 text-indigo-600" />
              </div>
              Candidate Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="w-12 h-12">
                <AvatarImage src={userProfile?.profilePicture} alt={userProfile?.name} />
                <AvatarFallback>
                  {(userProfile?.name || 'U')[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">{userProfile?.name}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{userProfile?.email}</p>
              </div>
            </div>
            
            {userProfile?.resumeUrl && (
              <Button variant="outline" size="sm" className="w-full">
                <FileText className="w-4 h-4 mr-2" />
                View Resume
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
