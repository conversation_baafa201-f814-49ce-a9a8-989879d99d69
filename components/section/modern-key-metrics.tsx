'use client';

import { Award, TrendingUp } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON>s, <PERSON>Axi<PERSON> } from 'recharts';

import { Card, CardContent } from '@camped-ui/card';

// Compact metrics display component
const CompactMetricsBar = ({ data }: { data: any[] }) => {
  console.log('Chart data:', data); // Debug log

  return (
    <div className="h-80 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          layout="horizontal"
          margin={{
            left: 80,
            right: 20,
            top: 20,
            bottom: 20,
          }}
        >
          <XAxis type="number" domain={[0, 100]} />
          <YAxis
            dataKey="name"
            type="category"
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            width={70}
            fontSize={12}
          />
          <Bar dataKey="value" fill="#3b82f6" radius={[0, 4, 4, 0]} barSize={24} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

interface ModernKeyMetricsProps {
  careerPractice: any;
}

export const ModernKeyMetrics = ({ careerPractice }: ModernKeyMetricsProps) => {
  const feedback = careerPractice?.feedback || {};

  // Calculate metrics from feedback
  const getMetricValue = (key: string, fallback: number = 0) => {
    const value = feedback[key]?.score || feedback[key] || fallback;
    console.log(`Getting value for ${key}:`, value, 'from feedback:', feedback[key]);
    return value;
  };

  // Prepare data for the horizontal bar chart
  const chartData = [
    {
      name: 'Communication',
      value: getMetricValue('communication', 0),
      fullName: 'Communication',
      description: 'Clarity & articulation',
    },
    {
      name: 'Confidence',
      value: getMetricValue('confidence', 0),
      fullName: 'Confidence',
      description: 'Self-assurance & poise',
    },
    {
      name: 'Clarity',
      value: getMetricValue('clarity', 0),
      fullName: 'Clarity',
      description: 'Clear expression',
    },
    {
      name: 'Passion',
      value: getMetricValue('passion', 0),
      fullName: 'Passion',
      description: 'Enthusiasm & drive',
    },
    {
      name: 'Impact',
      value: getMetricValue('impact', 0),
      fullName: 'Impact',
      description: 'Influence & effectiveness',
    },
    {
      name: 'Language',
      value: getMetricValue('language_proficiency', 0),
      fullName: 'Language Proficiency',
      description: 'Fluency & vocabulary',
    },
  ];

  // Show all data, including zeros for better visualization
  const validData = chartData;

  // Add test data if no real data exists
  const hasRealData = validData.some((item) => item.value > 0);
  const testData = [
    { name: 'Communication', value: 62, fullName: 'Communication' },
    { name: 'Confidence', value: 59, fullName: 'Confidence' },
    { name: 'Clarity', value: 58, fullName: 'Clarity' },
    { name: 'Passion', value: 59, fullName: 'Passion' },
    { name: 'Impact', value: 54, fullName: 'Impact' },
    { name: 'Language', value: 71, fullName: 'Language Proficiency' },
  ];

  const displayData = hasRealData ? validData : testData;

  // Find areas for improvement (lowest 3 scores)
  const sortedData = [...displayData].sort((a, b) => a.value - b.value);
  const improvementAreas = sortedData.slice(0, 3).map((item) => item.fullName);

  console.log('Feedback data:', feedback); // Debug log
  console.log('Valid data:', validData); // Debug log
  console.log('Display data:', displayData); // Debug log

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/20">
          <TrendingUp className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Key Performance Metrics
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Overview of candidate&apos;s performance across different areas
          </p>
        </div>
      </div>

      {/* Compact Chart Display */}
      {displayData && displayData.length > 0 ? (
        <Card>
          <CardContent className="p-6">
            <CompactMetricsBar data={displayData} />

            {/* Areas for improvement */}
            {improvementAreas.length > 0 && (
              <div className="mt-4 rounded-lg bg-orange-50 p-4 dark:bg-orange-950/20">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-orange-600 dark:text-orange-400">
                    Areas for improvement:
                  </span>{' '}
                  {improvementAreas.join(', ')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="flex h-40 items-center justify-center">
              <p className="text-center text-gray-500 dark:text-gray-400">
                No performance data available
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional insights */}
      {feedback?.overall_score && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
                <Award className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="mb-1 text-lg font-semibold text-gray-900 dark:text-white">
                  Performance Summary
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feedback.overall_score >= 80
                    ? 'Excellent performance across all areas. Strong candidate for the role.'
                    : feedback.overall_score >= 60
                    ? 'Good performance with some areas for improvement. Consider for next round.'
                    : 'Performance below expectations. May need additional evaluation.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
