'use client';

import { Award, Brain, Clock, Code, MessageSquare, Shield, Target, TrendingUp } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Progress } from '@camped-ui/progress';

interface MetricCardProps {
  title: string;
  value: number | string;
  maxValue?: number;
  icon: React.ReactNode;
  color: string;
  description?: string;
}

const MetricCard = ({ title, value, maxValue = 10, icon, color, description }: MetricCardProps) => {
  const numericValue = typeof value === 'string' ? parseFloat(value) || 0 : value;
  const percentage = maxValue ? (numericValue / maxValue) * 100 : numericValue;

  return (
    <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-md">
      <div className={cn('absolute left-0 top-0 h-1 w-full', color)} />

      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300">
            {title}
          </CardTitle>
          <div
            className={cn(
              'rounded-lg p-2',
              color.replace('bg-', 'bg-').replace('-500', '-100'),
              'dark:bg-opacity-20',
            )}
          >
            {icon}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="mb-2 flex items-baseline gap-2">
          <span className="text-2xl font-bold text-gray-900 dark:text-white">
            {typeof value === 'number' ? value.toFixed(1) : value}
          </span>
          {maxValue && typeof value === 'number' && (
            <span className="text-sm text-gray-500">/ {maxValue}</span>
          )}
        </div>

        {maxValue && typeof value === 'number' && (
          <Progress value={percentage} className="mb-2 h-2" />
        )}

        {description && <p className="text-xs text-gray-500 dark:text-gray-400">{description}</p>}
      </CardContent>
    </Card>
  );
};

interface ModernKeyMetricsProps {
  careerPractice: any;
  questions?: any[];
}

export const ModernKeyMetrics = ({ careerPractice, questions = [] }: ModernKeyMetricsProps) => {
  const feedback = careerPractice?.feedback || {};

  // Calculate metrics from feedback
  const getMetricValue = (key: string, fallback: number = 0) => {
    return feedback[key]?.score || feedback[key] || fallback;
  };

  // Calculate questions answered
  const questionsAnswered = questions.filter((q) => q?.isAnswered || q?.answer).length;
  const totalQuestions = questions.length;

  // Calculate average technical score
  const technicalScores = [
    getMetricValue('technical_skills'),
    getMetricValue('problem_solving'),
    getMetricValue('coding_skills'),
    getMetricValue('system_design'),
  ].filter((score) => score > 0);

  const avgTechnical =
    technicalScores.length > 0
      ? technicalScores.reduce((a, b) => a + b, 0) / technicalScores.length
      : 0;

  // Get interview duration
  const getDuration = () => {
    const startTime = careerPractice?.timing?.startTime;
    const completedTime = careerPractice?.timing?.completedTime;

    if (startTime && completedTime) {
      const start = new Date(startTime);
      const end = new Date(completedTime);
      const diffInMinutes = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
      return diffInMinutes;
    }
    return 0;
  };

  // Get proctoring score
  const getProctoringScore = () => {
    const riskLevel = feedback?.candidate_legitimacy?.flag_level?.toLowerCase();
    switch (riskLevel) {
      case 'minimal':
      case 'low':
        return 9;
      case 'medium':
        return 6;
      case 'high':
        return 3;
      default:
        return 8;
    }
  };

  // Get feedback generation status
  const getFeedbackStatus = () => {
    if (careerPractice?.timing?.feedBackGenerateTime) {
      return 'Generated';
    } else if (careerPractice?.timing?.completedTime) {
      return 'Pending';
    }
    return 'Not Started';
  };

  // Get video recordings count
  const getRecordingsCount = () => {
    const screenRecordings = careerPractice?.videoRecordings?.screen?.length || 0;
    const webcamRecordings = careerPractice?.videoRecordings?.webcam?.length || 0;
    return screenRecordings + webcamRecordings;
  };

  const metrics = [
    {
      title: 'Technical Skills',
      value: avgTechnical,
      maxValue: 10,
      icon: <Code className="h-5 w-5 text-blue-600" />,
      color: 'bg-blue-500',
      description: 'Coding & problem solving',
    },
    {
      title: 'Communication',
      value: getMetricValue('communication_skills', getMetricValue('communication', 0)),
      maxValue: 10,
      icon: <MessageSquare className="h-5 w-5 text-green-600" />,
      color: 'bg-green-500',
      description: 'Clarity & articulation',
    },
    {
      title: 'Problem Solving',
      value: getMetricValue('problem_solving', getMetricValue('analytical_thinking', 0)),
      maxValue: 10,
      icon: <Brain className="h-5 w-5 text-purple-600" />,
      color: 'bg-purple-500',
      description: 'Logical reasoning',
    },
    {
      title: 'Questions Answered',
      value: `${questionsAnswered}/${totalQuestions}`,
      icon: <Target className="h-5 w-5 text-orange-600" />,
      color: 'bg-orange-500',
      description: 'Completion rate',
    },
    {
      title: 'Interview Duration',
      value: `${getDuration()} min`,
      icon: <Clock className="h-5 w-5 text-indigo-600" />,
      color: 'bg-indigo-500',
      description: 'Time management',
    },
    {
      title: 'Proctoring Score',
      value: getProctoringScore(),
      maxValue: 10,
      icon: <Shield className="h-5 w-5 text-teal-600" />,
      color: 'bg-teal-500',
      description: 'Integrity assessment',
    },
    {
      title: 'Core Values',
      value: getMetricValue('coreValue') || getMetricValue('core_values', 0),
      maxValue: 10,
      icon: <Award className="h-5 w-5 text-pink-600" />,
      color: 'bg-pink-500',
      description: 'Cultural alignment',
    },
    {
      title: 'Job Fit',
      value: getMetricValue('job_fit', 0),
      maxValue: 10,
      icon: <TrendingUp className="h-5 w-5 text-cyan-600" />,
      color: 'bg-cyan-500',
      description: 'Role suitability',
    },
    {
      title: 'Feedback Status',
      value: getFeedbackStatus(),
      icon: <Award className="h-5 w-5 text-emerald-600" />,
      color: 'bg-emerald-500',
      description: 'Analysis completion',
    },
    {
      title: 'Recordings Available',
      value: `${getRecordingsCount()} files`,
      icon: <Award className="h-5 w-5 text-violet-600" />,
      color: 'bg-violet-500',
      description: 'Video recordings',
    },
  ];

  // Filter out metrics with no meaningful data
  const validMetrics = metrics.filter((metric) => {
    if (typeof metric.value === 'string') return metric.value !== '0/0' && metric.value !== '0 min';
    return metric.value > 0;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/20">
          <TrendingUp className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Key Performance Metrics
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Overview of candidate&apos;s performance across different areas
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {validMetrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            maxValue={metric.maxValue}
            icon={metric.icon}
            color={metric.color}
            description={metric.description}
          />
        ))}
      </div>

      {/* Additional insights */}
      {feedback?.overall_score && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
                <Award className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="mb-1 text-lg font-semibold text-gray-900 dark:text-white">
                  Performance Summary
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feedback.overall_score >= 8
                    ? 'Excellent performance across all areas. Strong candidate for the role.'
                    : feedback.overall_score >= 6
                    ? 'Good performance with some areas for improvement. Consider for next round.'
                    : 'Performance below expectations. May need additional evaluation.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
