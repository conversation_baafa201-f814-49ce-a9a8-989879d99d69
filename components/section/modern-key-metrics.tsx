'use client';

import { Award, TrendingUp } from 'lucide-react';

import { Card, CardContent } from '@camped-ui/card';

// Custom horizontal bar chart component
const CompactMetricsBar = ({ data }: { data: any[] }) => {
  return (
    <div className="space-y-4 py-4">
      {data.map((item, index) => (
        <div key={index} className="flex items-center gap-4">
          {/* Label */}
          <div className="w-24 text-right text-sm font-medium text-gray-700 dark:text-gray-300">
            {item.name}
          </div>

          {/* Bar container */}
          <div className="relative flex-1">
            <div className="h-6 rounded-full bg-gray-200 dark:bg-gray-700">
              <div
                className="flex h-6 items-center justify-end rounded-full bg-blue-500 pr-2 transition-all duration-500 ease-out"
                style={{ width: `${Math.max(item.value, 2)}%` }}
              >
                <span className="text-xs font-medium text-white">{item.value}</span>
              </div>
            </div>
          </div>

          {/* Scale markers */}
          <div className="w-8 text-xs text-gray-500">/100</div>
        </div>
      ))}

      {/* Scale reference */}
      <div className="mt-6 flex items-center gap-4 border-t border-gray-200 pt-4 dark:border-gray-700">
        <div className="w-24"></div>
        <div className="flex flex-1 justify-between text-xs text-gray-500">
          <span>0</span>
          <span>25</span>
          <span>50</span>
          <span>75</span>
          <span>100</span>
        </div>
        <div className="w-8"></div>
      </div>
    </div>
  );
};

interface ModernKeyMetricsProps {
  careerPractice: any;
}

export const ModernKeyMetrics = ({ careerPractice }: ModernKeyMetricsProps) => {
  const feedback = careerPractice?.feedback || {};

  // Calculate metrics from feedback - scores are stored directly as numbers
  const getMetricValue = (key: string, fallback: number = 0) => {
    const value = feedback[key];
    // Handle both direct numbers and nested score objects
    if (typeof value === 'number') return value;
    if (value && typeof value === 'object' && value.score) return value.score;
    return fallback;
  };

  // Prepare data for the horizontal bar chart
  const chartData = [
    {
      name: 'Communication',
      value: getMetricValue('communication', 0),
      fullName: 'Communication',
      description: 'Clarity & articulation',
    },
    {
      name: 'Confidence',
      value: getMetricValue('confidence', 0),
      fullName: 'Confidence',
      description: 'Self-assurance & poise',
    },
    {
      name: 'Clarity',
      value: getMetricValue('clarity', 0),
      fullName: 'Clarity',
      description: 'Clear expression',
    },
    {
      name: 'Passion',
      value: getMetricValue('passion', 0),
      fullName: 'Passion',
      description: 'Enthusiasm & drive',
    },
    {
      name: 'Impact',
      value: getMetricValue('impact', 0),
      fullName: 'Impact',
      description: 'Influence & effectiveness',
    },
    {
      name: 'Language',
      value: getMetricValue('language_proficiency', 0),
      fullName: 'Language Proficiency',
      description: 'Fluency & vocabulary',
    },
  ];

  // Filter out metrics with meaningful data (> 0)
  const validData = chartData.filter((item) => item.value > 0);
  const hasData = validData.length > 0;

  // Find areas for improvement (lowest 3 scores) only if we have data
  const sortedData = hasData ? [...validData].sort((a, b) => a.value - b.value) : [];
  const improvementAreas = sortedData.slice(0, 3).map((item) => item.fullName);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/20">
          <TrendingUp className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Key Performance Metrics
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Overview of candidate&apos;s performance across different areas
          </p>
        </div>
      </div>

      {/* Compact Chart Display */}
      {hasData ? (
        <Card>
          <CardContent className="p-6">
            <CompactMetricsBar data={validData} />

            {/* Areas for improvement */}
            {improvementAreas.length > 0 && (
              <div className="mt-4 rounded-lg bg-orange-50 p-4 dark:bg-orange-950/20">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-orange-600 dark:text-orange-400">
                    Areas for improvement:
                  </span>{' '}
                  {improvementAreas.join(', ')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="flex h-40 items-center justify-center">
              <p className="text-center text-gray-500 dark:text-gray-400">
                No performance data available
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional insights */}
      {feedback?.overall_score && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
                <Award className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="mb-1 text-lg font-semibold text-gray-900 dark:text-white">
                  Performance Summary
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feedback.overall_score >= 80
                    ? 'Excellent performance across all areas. Strong candidate for the role.'
                    : feedback.overall_score >= 60
                    ? 'Good performance with some areas for improvement. Consider for next round.'
                    : 'Performance below expectations. May need additional evaluation.'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
