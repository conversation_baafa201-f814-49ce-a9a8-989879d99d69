'use client';

import { useState } from 'react';

import { format } from 'date-fns';
import {
  AlertTriangle,
  Calendar,
  Download,
  FileText,
  MessageSquare,
  Send,
  Sparkles,
  Users,
  Video,
  XCircle,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@camped-ui/tabs';
import { Textarea } from '@camped-ui/textarea';

interface OptimizedInterviewSidebarProps {
  careerPractice: any;
  userProfile?: any;
  user?: any;
  members?: any;
  comment?: any[];
  newComment?: string;
  setNewComment?: (value: string) => void;
  handleAddComment?: () => void;
  handleResend?: (data: any) => void;
  handleReject?: (data: any) => void;
  handleGenerateFeedback?: (id: string) => void;
  setShowReScheduleModal?: (show: boolean) => void;
  router?: any;
  isPublic?: boolean;
  loading?: boolean;
  isGenerating?: boolean;
  disableFeedback?: boolean;
  isLoading?: boolean;
  platform?: string;
  timeAgo?: (date: string) => string;
}

export const OptimizedInterviewSidebar = ({
  careerPractice,
  userProfile,
  user,
  members,
  comment = [],
  newComment = '',
  setNewComment,
  handleAddComment,
  handleResend,
  handleReject,
  handleGenerateFeedback,
  setShowReScheduleModal,
  router,
  isPublic = false,
  loading = false,
  isGenerating = false,
  disableFeedback = false,
  isLoading = false,
  platform,
  timeAgo
}: OptimizedInterviewSidebarProps) => {
  
  const interviewers = careerPractice?.VideoCallInterview?.[0]?.interviewers || [];

  return (
    <div className="flex w-full max-w-[420px] flex-col gap-4">
      {/* Primary Actions - Always Visible */}
      {!isPublic && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* Generate Feedback - Most Important Action */}
              {!careerPractice?.timing?.feedBackGenerateTime && (
                <Button
                  className="w-full h-12 text-base font-medium bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  onClick={() => {
                    handleGenerateFeedback?.(careerPractice?.id);
                  }}
                  disabled={isGenerating || careerPractice?.timing?.feedBackGenerateTime}
                >
                  <Sparkles className="w-5 h-5 mr-2" />
                  {isGenerating ? 'Generating AI Feedback...' : 'Generate AI Feedback'}
                </Button>
              )}

              {/* Secondary Actions Row */}
              <div className="flex gap-2">
                {!careerPractice?.timing?.inviteTime && !careerPractice?.isPlacement && (
                  <Button
                    className="flex-1"
                    variant="outline"
                    onClick={() => {
                      handleResend?.({ id: careerPractice?.id, isInvite: true });
                    }}
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Invite
                  </Button>
                )}

                {platform === 'hire' && (
                  <Button
                    className="flex-1"
                    variant="destructive"
                    disabled={disableFeedback || isLoading}
                    onClick={() => handleReject?.({ id: careerPractice?.id, withLink: false })}
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Reject
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Proctoring Warnings - High Priority */}
      {(careerPractice?.proctorWarnings?.tabSwitch || careerPractice?.proctorWarnings?.fullScreen) && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <h3 className="font-semibold text-orange-800 dark:text-orange-200">Proctoring Alerts</h3>
            </div>
            <div className="space-y-2">
              {careerPractice.proctorWarnings.tabSwitch && (
                <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                  <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                  <span>Tab switches: {careerPractice.proctorWarnings.tabSwitch}</span>
                </div>
              )}
              {careerPractice.proctorWarnings.fullScreen && (
                <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                  <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                  <span>Full screen exits: {careerPractice.proctorWarnings.fullScreen}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabbed Content */}
      <Card className="flex-1">
        <Tabs defaultValue="candidate" className="w-full">
          <CardHeader className="pb-2">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="candidate" className="text-xs">
                <Users className="w-4 h-4 mr-1" />
                Candidate
              </TabsTrigger>
              <TabsTrigger value="notes" className="text-xs">
                <MessageSquare className="w-4 h-4 mr-1" />
                Notes
              </TabsTrigger>
              <TabsTrigger value="details" className="text-xs">
                <FileText className="w-4 h-4 mr-1" />
                Details
              </TabsTrigger>
            </TabsList>
          </CardHeader>

          <CardContent className="p-4 pt-2">
            {/* Candidate Tab */}
            <TabsContent value="candidate" className="mt-0 space-y-4">
              {userProfile && (
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={userProfile?.profilePicture} alt={userProfile?.name} />
                      <AvatarFallback>{(userProfile?.name || 'U')[0].toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{userProfile?.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{userProfile?.email}</p>
                    </div>
                  </div>
                  
                  {userProfile?.resumeUrl && (
                    <Button variant="outline" size="sm" className="w-full mb-4">
                      <Download className="w-4 h-4 mr-2" />
                      Download Resume
                    </Button>
                  )}
                </div>
              )}

              {/* Video Interview Info */}
              {careerPractice?.VideoCallInterview?.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4 text-green-600" />
                    <h4 className="font-medium">Video Interview</h4>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Scheduled:</span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {format(new Date(careerPractice.VideoCallInterview[0]?.scheduleTime), 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    
                    {interviewers?.length > 0 && (
                      <div>
                        <span className="text-gray-600 dark:text-gray-400 block mb-2">Interviewers:</span>
                        <div className="space-y-1">
                          {interviewers.map((interviewer: any) => (
                            <div key={interviewer.userId} className="flex items-center gap-2">
                              <Avatar className="h-5 w-5">
                                <AvatarFallback className="text-xs">
                                  {(interviewer?.user?.userProfile?.fullName || interviewer?.user?.email || 'U')[0].toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-xs text-gray-900 dark:text-gray-100">
                                {interviewer?.user?.userProfile?.fullName || interviewer?.user?.email}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="pt-2">
                    {careerPractice?.VideoCallInterview[0]?.meetingStatus === 'COMPLETED' && 
                     careerPractice?.VideoCallInterview[0]?.s3RecordingId ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          router?.push(`/video-interviews/${careerPractice?.VideoCallInterview[0]?.id}`);
                        }}
                      >
                        <Video className="mr-2 h-4 w-4" />
                        View Recording
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          setShowReScheduleModal?.(true);
                        }}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        Reschedule
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Notes Tab */}
            <TabsContent value="notes" className="mt-0">
              <div className="space-y-4">
                <div className="h-[300px] overflow-y-auto space-y-2">
                  {comment.length === 0 ? (
                    <p className="py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                      No notes yet. Add the first note below.
                    </p>
                  ) : (
                    comment.map((note, index) => (
                      <div key={index} className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800">
                        <div className="mb-2 flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                            {note?.commentBy?.userProfile?.fullName || note?.commentBy?.email}
                          </span>
                          <span className="text-xs text-gray-500">{timeAgo?.(note?.date)}</span>
                        </div>
                        <p className="text-sm text-gray-900 dark:text-gray-100">{note?.comment}</p>
                      </div>
                    ))
                  )}
                </div>
                
                <div className="space-y-2">
                  <Textarea
                    placeholder="Add a note..."
                    value={newComment}
                    onChange={(e) => setNewComment?.(e.target.value)}
                    className="min-h-[80px] resize-none"
                  />
                  <Button
                    size="sm"
                    className="w-full"
                    disabled={loading || !newComment.trim()}
                    onClick={handleAddComment}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Add Note
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Details Tab */}
            <TabsContent value="details" className="mt-0">
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Position:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{careerPractice?.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Level:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{careerPractice?.level}</span>
                  </div>
                  {careerPractice?.isPlacement && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Type:</span>
                      <Badge variant="secondary" className="text-xs">Interviewathon</Badge>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </CardContent>
        </Tabs>
      </Card>
    </div>
  );
};
