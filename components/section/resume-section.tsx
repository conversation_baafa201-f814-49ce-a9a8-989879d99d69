'use client';

import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@camped-ui/tooltip';

import ProgressCircle from '../ui/progress-circle';

export const ResumeSection = ({ feedback, resumeUrl }) => {
  if (!feedback) return null;

  const statusOrder = {
    found: 1,
    relevant: 2,
    not_found: 3,
  };

  // Convert the keywords object to an array of [key, value] pairs
  const keywordArray = Object.entries(feedback?.skills_required);

  // Sort the array based on the statusOrder
  const sortedKeywords = keywordArray.sort(([, statusA]: any, [, statusB]: any) => {
    return statusOrder[statusA] - statusOrder[statusB];
  });

  // Convert the sorted array back to an object
  const sortedKeywordsObject = Object.fromEntries(sortedKeywords);
  const value = Object.values(feedback?.skills_required ?? {})?.filter(
    (item: any) => ['found', 'relevant']?.includes(item),
  )?.length;
  const totalSkills = Object.keys(feedback?.skills_required ?? {})?.length;

  const getIcons = (status) => {
    if (status === 'found') {
      return { icon: 'CheckCircle2', color: 'text-green-700 dark:text-green-500' };
    } else if (status === 'relevant') {
      return { icon: 'CheckCircle2', color: 'text-orange-400' };
    } else if (status === 'not_found') {
      return { icon: 'CircleAlert', color: 'text-destructive' };
    }
  };

  const colorRanges = [
    { scoreRange: [0, 50], color: 'text-destructive' }, // Red
    { scoreRange: [50, 80], color: 'text-orange-400' }, // Orange
    { scoreRange: [80, 100], color: 'text-green-700 dark:text-green-500' }, // Green
  ];

  const strokeColor =
    colorRanges.find(
      (range) =>
        feedback?.skills_matching_score >= range.scoreRange[0] &&
        feedback?.skills_matching_score <= range.scoreRange[1],
    )?.color || '#000000';

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
      {/* Header Section */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
            <Icon name="FileText" className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Resume Analysis</h2>
            <p className="text-gray-600 dark:text-gray-300">
              Skills matching and resume evaluation
            </p>
          </div>
        </div>
        {resumeUrl && (
          <a target="_blank" href={resumeUrl}>
            <Button size="sm" className="h-10">
              <Icon name="Download" className="mr-2 h-4 w-4" />
              Download Resume
            </Button>
          </a>
        )}
      </div>

      {/* Score and Summary Section */}
      <div className="mb-6 grid gap-6 md:grid-cols-2">
        {/* Score Circle */}
        <div className="flex flex-col items-center justify-center space-y-4 rounded-lg bg-gray-50 p-6 dark:bg-gray-700">
          <ProgressCircle
            percent={feedback?.skills_matching_score}
            width={120}
            height={120}
            strokeWidth={8}
            textSize="2xl"
          />
          <div className="text-center">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Skills Match Score
            </p>
            <p className={`text-lg font-bold ${strokeColor}`}>
              {value} out of {totalSkills} skills found
            </p>
          </div>
        </div>

        {/* Summary Text */}
        <div className="flex flex-col justify-center space-y-4">
          <div>
            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
              Analysis Summary
            </h3>
            <p className="leading-relaxed text-gray-700 dark:text-gray-300">{feedback?.comments}</p>
          </div>
        </div>
      </div>

      {/* Skills Breakdown Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Skills Breakdown</h3>
          <Tooltip className="cursor-pointer">
            <TooltipTrigger>
              <Icon name="info" className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </TooltipTrigger>
            <TooltipContent>
              <div className="flex flex-col gap-2">
                <div className="flex items-center space-x-2">
                  <Icon
                    name="CheckCircle2"
                    className="text-green-700 dark:text-green-500"
                    size={16}
                  />
                  <span className="text-sm">Skill found</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon name="CheckCircle2" className="text-orange-400" size={16} />
                  <span className="text-sm">Relevant skill found</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon name="CircleAlert" className="text-destructive" size={16} />
                  <span className="text-sm">Skill not found</span>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>

        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {Object.entries(sortedKeywordsObject ?? {}).map(([key, value], index) => {
            const metaData = getIcons(value);
            return (
              <div
                key={index}
                className="flex items-center space-x-3 rounded-lg border bg-gray-50 p-3 dark:border-gray-600 dark:bg-gray-700"
              >
                <Icon name={metaData?.icon} className={metaData?.color} size={18} />
                <span className="text-sm font-medium capitalize text-gray-900 dark:text-gray-100">
                  {key}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
