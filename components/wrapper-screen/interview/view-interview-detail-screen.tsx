'use client';

import React, { useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { InterviewDurationCard } from '@/components/cards/admin/interview-duration';
import { InterviewProctoringCard } from '@/components/cards/admin/interview-proctoring';
import { InterviewQuestionTiming } from '@/components/cards/admin/interview-question-timing';
import { CodingResultStatusCard } from '@/components/cards/coding-result-status-card';
import { EdgeCaseCard } from '@/components/cards/edge-case';
import { ReScheduleInterviewModal } from '@/components/interview/interview-reschedule-Modal';
import { RejectModal } from '@/components/interview/rejectModal';
import { WrittenQuestion } from '@/components/organization/written-interview-question';
import { LeftSide } from '@/components/profile/LeftSide';
import { RightSide } from '@/components/profile/RightSide';
import {
  DetailScoreSection,
  DetailsSection,
  FeedBackSection,
  PerformanceScoreSection,
} from '@/components/section';
import { DecisionCard } from '@/components/section/decision-card';
import { EditableDecisionCard } from '@/components/section/editable-decision-card';
import { ModernDetailedAnalysis } from '@/components/section/modern-detailed-analysis';
import { ModernExecutiveSummary } from '@/components/section/modern-executive-summary';
import { ModernInterviewHero } from '@/components/section/modern-interview-hero';
import { ModernKeyMetrics } from '@/components/section/modern-key-metrics';
import { ResumeSection } from '@/components/section/resume-section';
import { TopResultSection } from '@/components/section/top-result-section';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VideoPlayer } from '@/components/video/player';
import { languageOptions } from '@/constants/languageOptions';
import { useHandleGenerateFeedback } from '@/hooks/mutation/use-handle-generate-feedback';
import { useHandleReject } from '@/hooks/mutation/use-handle-reject';
import { useHandleResend } from '@/hooks/mutation/use-handle-resend';
import { useHandleScheduleCall } from '@/hooks/mutation/use-handle-schedule-call';
import { Icon } from '@/icons';
import { updateCareerPractice } from '@/services/apicall';
import { capitalizeFirstLetter } from '@/utils/string.helper';
import { timeAgo } from '@/utils/time-ago';
import {
  SandpackCodeEditor,
  SandpackFileExplorer,
  SandpackLayout,
  SandpackPreview,
  SandpackProvider,
} from '@codesandbox/sandpack-react';
import base64 from 'base-64';
import { format } from 'date-fns';
import {
  AlertTriangle,
  Download,
  MessageSquare,
  Send,
  Sparkles,
  XCircle,
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import ReactMarkdown from 'react-markdown';

import { Button } from '@camped-ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Textarea } from '@camped-ui/textarea';

import { MultipleChoiceQuestion } from '../../organization/multiple-choice-question';
import PageHeader from '../../page-header';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '../../ui/resizeable';
import { VideoInterviewDetailScreen } from './video-interview-detail-screen';

export const ViewInterviewDetailScreen = ({
  careerPractice,
  userProfile,
  user,
  userId,
  members,
  isPublic = false,
  platform,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showReScheduleModal, setShowReScheduleModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [flag, setFlag] = useState(careerPractice?.feedback?.overall_recommendation?.decision);
  const [reason, setReason] = useState(careerPractice?.feedback?.overall_recommendation?.reason);
  const [comment, setComment] = useState(careerPractice?.interviewerComments ?? []);
  const { theme } = useTheme();
  const router = useRouter();
  // Update the interviewers constant at the top of the component
  const interviewers = members?.items?.filter(
    (member) => careerPractice?.VideoCallInterview[0]?.interviewerId?.includes(member?.userId),
  );
  const handleOnSave = async () => {
    toast.loading('Saving changes');
    const update = await updateCareerPractice({
      id: careerPractice?.id,
      data: {
        feedback: {
          ...careerPractice?.feedback,
          overall_recommendation: {
            reason,
            decision: flag,
          },
        },
      },
    });

    toast.remove();
    if (update?.careerPractice?.id) {
      toast.success('Changes saved');
      router.refresh();
    } else {
      toast.error('Failed to save changes');
    }
  };
  const multipleChoiceQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'multiple-choice',
  );
  const videoQuestion = careerPractice?.conversation?.filter(
    (item) => item.round === 'video-interview',
  );
  const codingQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'coding-interview',
  );
  const frontendQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'frontend-interview',
  );
  const writtenQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'written-interview',
  );

  const { mutate: handleResend } = useHandleResend({ eventId: careerPractice?.eventId });
  const mergeAllQuestions = () => {
    let questions = [...videoQuestion, ...codingQuestions, ...frontendQuestions] as any;

    if (multipleChoiceQuestions?.length) {
      questions = [...questions, multipleChoiceQuestions];
    }
    if (writtenQuestions?.length) {
      questions = [...questions, writtenQuestions];
    }

    return {
      questions,
    };
  };

  const availableRound = () => {
    let availableRound: any = [];
    if (videoQuestion?.length > 0) {
      availableRound.push('Video Round');
    }
    if (codingQuestions?.length > 0 || frontendQuestions?.length > 0) {
      availableRound.push('Coding Round');
    }
    if (multipleChoiceQuestions?.length > 0) {
      availableRound.push('MCQs');
    }
    if (writtenQuestions?.length > 0) {
      availableRound.push('Written');
    }
    return availableRound;
  };
  const { questions }: { questions: any } = mergeAllQuestions();

  const form = useForm({
    defaultValues: {
      status: careerPractice?.comments?.[0]?.status ?? 'Rejected',
      feedback:
        careerPractice?.comments?.[0]?.message ??
        'Thank you for your application and the time you invested in interviewing with us. We regret to inform you that we will not be proceeding with your candidacy for this position. We encourage you to apply for future opportunities as they become available.',
      meetingType: careerPractice?.VideoCallInterview[0]?.meetingType ?? 'videoCall',
      meetingDateTime: new Date(
        careerPractice?.VideoCallInterview[0]?.scheduleTime ?? new Date(),
      ).toISOString(),
      selectedDate: new Date(
        careerPractice?.VideoCallInterview[0]?.scheduleTime ?? new Date(),
      ).toISOString(),
      interviewer:
        members?.items?.filter(
          (member) =>
            careerPractice?.VideoCallInterview[0]?.interviewerId?.includes(member?.userId),
        ) ?? null,
      address: careerPractice?.VideoCallInterview[0]?.address,
      meetingDuration: careerPractice?.VideoCallInterview[0]?.interviewDuration ?? '60',
    },
  });
  const { mutate: handleReject } = useHandleReject({
    showRejectModal,
    setSelectedCandidate: () => {},
    setShowRejectModal,
    userId,
    candidateStatus: form.watch('status'),
    candidateFeedback: form.watch('feedback'),
    setIsLoading,
  });
  const { mutate: handleSchedule } = useHandleScheduleCall({
    form,
    careerPracticeId: careerPractice?.id,
    setShowRejectModal,
    platform,
  });
  const { mutate: handleReSchedule } = useHandleScheduleCall({
    form,
    careerPracticeId: careerPractice?.id,
    setShowRejectModal: setShowReScheduleModal,
    platform,
    isUpdate: true,
  });
  const { mutate: handleGenerateFeedback } = useHandleGenerateFeedback({
    careerPractice,
    round: careerPractice?.isPlacement ? 'Interviewathon' : 'Interview',
  });
  const handleAddComment = async () => {
    setLoading(true);
    toast.loading('Adding note..');
    const update = await updateCareerPractice({
      id: careerPractice?.id,
      data: {
        interviewerComments: [
          ...(comment?.length > 0
            ? comment?.map((item) => ({ ...item, commentBy: item?.commentBy?.id }))
            : []),
          { comment: newComment, commentBy: userId, date: new Date() },
        ],
      },
    });
    toast.remove();
    setLoading(false);
    if (update?.careerPractice) {
      toast.success(`Note added`);
      setComment(update?.careerPractice?.interviewerComments);
      setNewComment('');
    } else {
      toast.error(`Failed to add note`);
    }
  };

  useEffect(() => {
    setVideoUrl(questions?.[activeIndex]?.videoUrl);
  }, [activeIndex]);

  const initialSourceCode = questions?.[activeIndex]?.source_code;

  const sourceCode =
    initialSourceCode && questions?.[activeIndex]?.round === 'coding-interview'
      ? base64.decode(initialSourceCode)
      : '';
  let prev = {
    round: 'video-interview',
    qno: -1,
  };
  const language = languageOptions?.find(
    (item) => item?.id === questions?.[activeIndex]?.languages?.[0],
  );
  const disableFeedback = ['Move to next round', 'Rejected', 'Accepted']?.includes(
    careerPractice?.comments?.[0]?.status,
  );

  return (
    <>
      {!isPublic && (
        <PageHeader
          title={`Performance Analysis - ${
            userProfile?.fullName === '' || !userProfile?.fullName
              ? user?.email
              : userProfile?.fullName
          }`}
          hasBack
        />
      )}
      <div className="mt-2 flex w-full flex-col">
        <div className="flex w-full flex-col gap-4">
          {!careerPractice?.conversation?.[0]?.room_name ? (
            <Tabs defaultValue={`overall`} className="w-full">
              <TabsList className="flex h-12 w-full items-start justify-start gap-2 overflow-x-auto">
                <TabsTrigger value="overall" className="flex h-10 items-center justify-center">
                  Overview
                </TabsTrigger>
                <TabsTrigger value="notes" className="flex h-10 items-center justify-center">
                  Notes
                </TabsTrigger>
                <TabsTrigger value="question" className="flex h-10 items-center justify-center">
                  Conversation
                </TabsTrigger>
                <TabsTrigger value="recordings" className="flex h-10 items-center justify-center">
                  Interview Recordings
                </TabsTrigger>
              </TabsList>
              <TabsContent value="overall" className="flex w-full flex-col gap-8">
                {!careerPractice?.timing?.completedTime && !careerPractice?.feedback?.resume && (
                  <EdgeCaseCard
                    title="No Feedback Available"
                    description="Once the candidate has completed the interview, the feedback will be available."
                  />
                )}

                {/* Action Bar - Always visible on Overview */}
                {!isPublic && (
                  <div className="flex gap-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-900">
                    {!careerPractice?.timing?.feedBackGenerateTime && (
                      <Button
                        className="h-12 bg-gradient-to-r from-blue-600 to-purple-600 text-base font-medium hover:from-blue-700 hover:to-purple-700"
                        onClick={() => {
                          handleGenerateFeedback?.(careerPractice?.id);
                        }}
                        disabled={isGenerating || careerPractice?.timing?.feedBackGenerateTime}
                      >
                        <Sparkles className="mr-2 h-5 w-5" />
                        {isGenerating ? 'Generating AI Feedback...' : 'Generate AI Feedback'}
                      </Button>
                    )}

                    {!careerPractice?.timing?.inviteTime && !careerPractice?.isPlacement && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          handleResend?.({ id: careerPractice?.id, isInvite: true });
                        }}
                      >
                        <Send className="mr-2 h-4 w-4" />
                        Invite Candidate
                      </Button>
                    )}

                    {platform === 'hire' && (
                      <Button
                        variant="destructive"
                        disabled={disableFeedback || isLoading}
                        onClick={() => handleReject?.({ id: careerPractice?.id, withLink: false })}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        {careerPractice?.comments?.[0]?.status ?? 'Send Feedback'}
                      </Button>
                    )}
                  </div>
                )}

                {/* Proctoring Warnings */}
                {(careerPractice?.proctorWarnings?.tabSwitch ||
                  careerPractice?.proctorWarnings?.fullScreen) && (
                  <div className="rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-950/20">
                    <div className="mb-3 flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      <h3 className="font-semibold text-orange-800 dark:text-orange-200">
                        Proctoring Alerts
                      </h3>
                    </div>
                    <div className="space-y-2">
                      {careerPractice.proctorWarnings.tabSwitch && (
                        <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                          <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                          <span>
                            Tab switches detected: {careerPractice.proctorWarnings.tabSwitch} times
                          </span>
                        </div>
                      )}
                      {careerPractice.proctorWarnings.fullScreen && (
                        <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
                          <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                          <span>
                            Full screen exits: {careerPractice.proctorWarnings.fullScreen} times
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Resume Download - Quick Access */}
                {userProfile?.resumeUrl && (
                  <div className="flex justify-end">
                    <Button variant="outline" className="gap-2">
                      <Download className="h-4 w-4" />
                      Download Resume
                    </Button>
                  </div>
                )}

                {/* Modern Hero Section */}
                <ModernInterviewHero careerPractice={careerPractice} userProfile={userProfile} />

                {/* Modern Key Metrics */}
                {(careerPractice?.feedback?.overall_score ||
                  careerPractice?.feedback?.overall_score === 0) && (
                  <ModernKeyMetrics careerPractice={careerPractice} />
                )}

                {/* Modern Executive Summary */}
                {careerPractice?.feedback && (
                  <ModernExecutiveSummary careerPractice={careerPractice} />
                )}

                {/* Collapsible Detailed Analysis Section */}
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-gray-100 p-2 dark:bg-gray-800">
                      <svg
                        className="h-6 w-6 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Detailed Analysis
                      </h2>
                      <p className="text-gray-600 dark:text-gray-300">
                        Comprehensive breakdown of interview performance
                      </p>
                    </div>
                  </div>
                  <ModernDetailedAnalysis
                    careerPractice={careerPractice}
                    userProfile={userProfile}
                    questions={questions}
                    userId={userId}
                    reason={reason}
                    flag={flag}
                    setFlag={setFlag}
                    setReason={setReason}
                    handleOnSave={handleOnSave}
                  />
                </div>
              </TabsContent>
              <TabsContent value="recordings" className="flex w-full flex-col gap-4">
                {careerPractice?.videoRecordings?.screen?.length === 0 &&
                  careerPractice?.videoRecordings?.webcam?.length === 0 && (
                    <EdgeCaseCard
                      title="No Recordings Available"
                      description="Currently no recordings available."
                    />
                  )}
                {careerPractice?.videoRecordings?.screen?.map((recording: any, index: number) => (
                  <video
                    id="screen-remote-video"
                    key={index}
                    className="rounded-md"
                    src={recording}
                    controls
                    playsInline
                    autoPlay={true}
                    muted={false}
                    width="550" // You can adjust the size
                    height="300"
                  />
                ))}
                {careerPractice?.videoRecordings?.webcam?.map((recording: any, index: number) => (
                  <video
                    id="screen-remote-video"
                    className="rounded-md"
                    src={recording}
                    key={index}
                    controls
                    playsInline
                    autoPlay={true}
                    muted={false}
                    width="550" // You can adjust the size
                    height="300"
                  />
                ))}
              </TabsContent>

              {/* Notes Tab */}
              <TabsContent value="notes" className="flex w-full flex-col gap-6">
                <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
                  <div className="mb-6 flex items-center gap-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                      <MessageSquare className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Interview Notes
                      </h2>
                      <p className="text-gray-600 dark:text-gray-300">
                        Interviewer comments and observations
                      </p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div className="max-h-[500px] space-y-4 overflow-y-auto">
                      {comment.length === 0 ? (
                        <div className="py-12 text-center">
                          <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                          <p className="text-lg text-gray-500 dark:text-gray-400">No notes yet</p>
                          <p className="text-gray-400 dark:text-gray-500">
                            Add the first note below to get started
                          </p>
                        </div>
                      ) : (
                        comment.map((note, index) => (
                          <div key={index} className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
                            <div className="mb-3 flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                                  <span className="text-sm font-medium text-blue-600">
                                    {(note?.commentBy?.userProfile?.fullName ||
                                      note?.commentBy?.email ||
                                      'U')[0].toUpperCase()}
                                  </span>
                                </div>
                                <span className="font-medium text-gray-900 dark:text-gray-100">
                                  {note?.commentBy?.userProfile?.fullName || note?.commentBy?.email}
                                </span>
                              </div>
                              <span className="text-sm text-gray-500">{timeAgo?.(note?.date)}</span>
                            </div>
                            <p className="leading-relaxed text-gray-900 dark:text-gray-100">
                              {note?.comment}
                            </p>
                          </div>
                        ))
                      )}
                    </div>

                    <div className="border-t border-gray-200 pt-6 dark:border-gray-700">
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Add New Note
                      </label>
                      <Textarea
                        placeholder="Write your observations, feedback, or notes about the candidate..."
                        value={newComment}
                        onChange={(e) => setNewComment?.(e.target.value)}
                        className="mb-4 min-h-[120px] resize-none"
                      />
                      <Button
                        className="w-full"
                        disabled={loading || !newComment.trim()}
                        onClick={handleAddComment}
                      >
                        <Send className="mr-2 h-4 w-4" />
                        {loading ? 'Adding Note...' : 'Add Note'}
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="question" className="flex w-full flex-col gap-4">
                <Tabs defaultValue={`Q-${activeIndex}`} className="w-full">
                  <TabsList className="flex h-12 w-full items-start justify-start gap-2 overflow-x-auto">
                    {questions?.map((items, index) => {
                      const round = items?.round;
                      console.log({ round });

                      prev['qno'] = prev.qno + 1;
                      let qno = prev.qno;
                      if (prev.round !== round) {
                        prev = {
                          round,
                          qno: 0,
                        };
                        qno = 0;
                      }
                      return (
                        <TabsTrigger
                          key={index}
                          value={`Q-${index}`}
                          onClick={() => setActiveIndex(index)}
                          className="flex h-10 items-center justify-center"
                        >
                          {round
                            ? `${capitalizeFirstLetter(round[0])}${qno + 1}`
                            : items?.[0]?.round === 'written-interview'
                            ? 'Written'
                            : `MCQs`}
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>
                </Tabs>

                {Array.isArray(questions?.[activeIndex]) &&
                questions?.[activeIndex]?.[0]?.round === 'multiple-choice' ? (
                  <>
                    <MultipleChoiceQuestion questions={questions?.[activeIndex]} />
                  </>
                ) : Array.isArray(questions?.[activeIndex]) &&
                  questions?.[activeIndex]?.[0]?.round === 'written-interview' ? (
                  <WrittenQuestion questions={questions?.[activeIndex]} />
                ) : (
                  <>
                    {questions?.[activeIndex]?.question ? (
                      <div>
                        <h2 className="mb-2 text-left text-lg font-semibold">Question</h2>
                        <CardDescription>{questions?.[activeIndex]?.question}</CardDescription>
                      </div>
                    ) : null}
                    {questions?.[activeIndex]?.questionDescription ? (
                      questions?.[activeIndex]?.round === 'frontend-interview' ? (
                        <CardDescription className="w-full">
                          <ReactMarkdown className="markDown">
                            {questions?.[activeIndex]?.questionDescription?.replace(/\\n/g, '\n')}
                          </ReactMarkdown>
                        </CardDescription>
                      ) : (
                        <div>
                          <h3 className="my-2 text-lg font-semibold"> Description:</h3>
                          <CardDescription className="mb-2">
                            {questions?.[activeIndex]?.questionDescription}
                          </CardDescription>
                        </div>
                      )
                    ) : null}
                    {questions?.[activeIndex]?.round === 'coding-interview' &&
                    !questions?.[activeIndex]?.status &&
                    !questions?.[activeIndex]?.memory &&
                    !questions?.[activeIndex]?.time ? (
                      <div className="relative w-full">
                        <div className="left-0 mt-3 flex h-[400px] w-full items-center justify-center rounded-lg border bg-secondary opacity-50">
                          Candidate didn&apos;t attempt
                        </div>
                      </div>
                    ) : null}

                    {questions?.[activeIndex]?.round === 'frontend-interview' &&
                    !questions?.[activeIndex]?.isAnswered ? (
                      <div className="relative w-full">
                        <div className="left-0 mt-3 flex h-[400px] w-full items-center justify-center rounded-lg border bg-secondary opacity-50">
                          Candidate didn&apos;t attempt
                        </div>
                      </div>
                    ) : null}

                    {questions?.[activeIndex]?.round === 'video-interview' && (
                      <div className="relative w-full">
                        {videoUrl ? (
                          <VideoPlayer src={videoUrl} />
                        ) : (
                          <div className="left-0 mt-3 flex h-[400px] w-full items-center justify-center rounded-lg border bg-secondary opacity-50">
                            Candidate didn&apos;t attempt
                          </div>
                        )}
                      </div>
                    )}

                    {questions?.[activeIndex]?.round === 'video-interview' &&
                    questions?.[activeIndex]?.isAnswered ? (
                      <InterviewQuestionTiming questions={questions} activeIndex={activeIndex} />
                    ) : null}

                    {questions?.[activeIndex]?.answer ? (
                      <Card>
                        <CardHeader className="p-3">
                          <CardTitle className="text-left text-xl font-semibold">
                            Transcript
                          </CardTitle>
                        </CardHeader>
                        <Separator />
                        <CardDescription className="p-4">
                          {questions?.[activeIndex]?.answer}
                        </CardDescription>
                      </Card>
                    ) : null}

                    {questions?.[activeIndex]?.round === 'coding-interview' &&
                    questions?.[activeIndex]?.isAnswered ? (
                      <div className="grid grid-cols-4 gap-4">
                        <CodingResultStatusCard
                          title="Compile Language"
                          description={capitalizeFirstLetter(
                            questions?.[activeIndex]?.language ?? language?.value,
                          )}
                        />
                        <CodingResultStatusCard
                          title="Compile Time"
                          description={questions?.[activeIndex]?.time ?? '-'}
                        />
                        <CodingResultStatusCard
                          title="Compile Memory"
                          description={questions?.[activeIndex]?.memory ?? '-'}
                        />
                        <CodingResultStatusCard
                          title="Compile Status"
                          description={questions?.[activeIndex]?.status ?? '-'}
                        />
                      </div>
                    ) : null}

                    {questions?.[activeIndex]?.round === 'coding-interview' &&
                    questions?.[activeIndex]?.status &&
                    questions?.[activeIndex]?.memory &&
                    questions?.[activeIndex]?.time ? (
                      <FeedBackSection short_summary={sourceCode} title="Coding Input" />
                    ) : null}

                    {questions?.[activeIndex]?.round === 'frontend-interview' &&
                    questions?.[activeIndex]?.isAnswered ? (
                      <>
                        <SandpackProvider
                          key={`Sandpack${activeIndex}`}
                          template={questions?.[activeIndex]?.source_code?.templateName || 'react'}
                          style={{ width: '100%' }}
                          theme={theme === 'dark' ? 'dark' : 'light'}
                          options={{ autoReload: true }}
                          customSetup={{
                            dependencies: questions?.[activeIndex]?.source_code?.dependencies || {},
                            environment:
                              questions?.[activeIndex]?.source_code?.environment ||
                              'create-react-app',
                          }}
                          files={getTemplate(questions?.[activeIndex]?.source_code?.files)}
                          className="w-full"
                        >
                          <ResizablePanelGroup
                            direction="horizontal"
                            className="flex h-full w-full"
                          >
                            <SandpackLayout
                              style={{
                                height: `calc(100vh - 65px)`,
                                display: 'flex',
                                width: '100%',
                              }}
                            >
                              <ResizablePanelGroup direction="vertical">
                                <ResizablePanel defaultSize={50}>
                                  <div className="flex h-full w-full rounded border">
                                    <SideBar />
                                    <Separator orientation="vertical" />
                                    <SandpackCodeEditor
                                      showTabs
                                      showLineNumbers={true}
                                      showInlineErrors
                                      wrapContent
                                      closableTabs
                                      style={{ height: '100%', width: '100%' }}
                                      readOnly={true}
                                      showReadOnly={false}
                                      className="cursor-default"
                                    />
                                  </div>
                                </ResizablePanel>
                                <ResizableHandle withHandle />
                                <ResizablePanel defaultSize={50} minSize={10} maxSize={90}>
                                  <SandpackPreview
                                    showOpenInCodeSandbox={false}
                                    style={{ height: '100%' }}
                                    showNavigator
                                  />
                                </ResizablePanel>
                              </ResizablePanelGroup>
                            </SandpackLayout>
                          </ResizablePanelGroup>
                        </SandpackProvider>
                      </>
                    ) : null}

                    <>
                      <PerformanceScoreSection data={questions?.[activeIndex]?.feedback} />
                      <FeedBackSection
                        short_summary={(questions?.[activeIndex]?.feedback as any)?.short_summary}
                        title="Feedback"
                      />
                      <DetailScoreSection
                        data={{
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.camera_angle && {
                            ['Camera Angle']: {
                              score: undefined,
                              feedback: questions[activeIndex].feedback.proctoring.camera_angle,
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'eye_contact_&_posture'
                          ] && {
                            ['Eye Contact & Posture']: {
                              score: undefined,
                              feedback:
                                questions[activeIndex].feedback.proctoring['eye_contact_&_posture'],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'suspicious_activity'
                          ] && {
                            ['Suspicious Activity']: {
                              score: undefined,
                              feedback:
                                questions[activeIndex].feedback.proctoring['suspicious_activity'],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'professional_attire'
                          ] && {
                            ['Professional Attire']: {
                              score: undefined,
                              feedback:
                                questions[activeIndex].feedback.proctoring['professional_attire'],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'emotional_state'
                          ] && {
                            ['Emotional State']: {
                              score: undefined,
                              feedback:
                                questions?.[activeIndex]?.feedback?.proctoring['emotional_state'],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'candidate_movement'
                          ] && {
                            ['Candidate Movement']: {
                              score: undefined,
                              feedback:
                                questions?.[activeIndex]?.feedback?.proctoring[
                                  'candidate_movement'
                                ],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.['background'] && {
                            ['Background']: {
                              score: undefined,
                              feedback:
                                questions?.[activeIndex]?.feedback?.proctoring['background'],
                            },
                          }),
                          ...(questions?.[activeIndex]?.feedback?.proctoring?.[
                            'words_per_minute'
                          ] && {
                            ['Word Per Minute']: {
                              score: undefined,
                              feedback:
                                questions?.[activeIndex]?.feedback?.proctoring['words_per_minute'],
                            },
                          }),
                        }}
                        title="Proctoring Details"
                        user={userId}
                      />

                      {questions?.[activeIndex]?.isAnswered && (
                        <DetailScoreSection
                          data={questions?.[activeIndex]?.feedback}
                          title="Advanced Analysis"
                          user={userId}
                        />
                      )}
                      <FeedBackSection
                        short_summary={(questions?.[activeIndex]?.feedback as any)?.strengths}
                        title="Strength"
                      />
                      <FeedBackSection
                        short_summary={
                          (questions?.[activeIndex]?.feedback as any)?.areas_of_improvement
                        }
                        title="Area of Improvement"
                      />
                      <FeedBackSection
                        short_summary={
                          (questions?.[activeIndex]?.feedback as any)?.suggested_implementation
                        }
                        title="Suggested Implementation"
                      />
                      <FeedBackSection
                        short_summary={
                          (questions?.[activeIndex]?.feedback as any)?.alternate_answer
                        }
                        title="Rephrasing suggestions"
                      />
                    </>
                  </>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <VideoInterviewDetailScreen
              meeting={{ ...careerPractice, s3RecordingId: careerPractice.conversation?.[0]?.s3Id }}
              userId={userId}
              resumeUrl={userProfile?.resumeUrl}
              isAiInterview={true}
            />
          )}
        </div>
      </div>

      <RejectModal
        form={form}
        members={members}
        handleSubmit={(withLink, sendEmail) =>
          handleReject({ id: careerPractice?.id, withLink, sendEmail })
        }
        setShowPopup={setShowRejectModal}
        showPopup={showRejectModal}
        handleSchedule={handleSchedule}
        platform={platform}
      />
      <ReScheduleInterviewModal
        form={form}
        members={members}
        setShowPopup={setShowReScheduleModal}
        showPopup={showReScheduleModal}
        handleSchedule={handleReSchedule}
        platform={platform}
      />
    </>
  );
};

const getTemplate = (template) => {
  const arrayToObject = {};

  template?.forEach(({ fileName, fileData }) => {
    arrayToObject[fileName] = base64.decode(fileData);
  });

  return arrayToObject;
};

const SideBar = () => {
  const [activeTab, setActiveTab] = useState('');

  return (
    <>
      <div className="flex w-12 flex-col gap-4 p-2">
        <div
          onClick={() =>
            activeTab === 'file-explore' ? setActiveTab('') : setActiveTab('file-explore')
          }
          className={`cursor-pointer hover:text-gray-500 ${
            activeTab === 'file-explore' ? 'text-gray-500' : ''
          }`}
        >
          <Icon name="Files" className="h-6 w-6" />
        </div>
      </div>
      {activeTab === 'file-explore' ? (
        <SandpackFileExplorer
          style={{
            flex: '0 0 150px',
          }}
        />
      ) : null}
    </>
  );
};
