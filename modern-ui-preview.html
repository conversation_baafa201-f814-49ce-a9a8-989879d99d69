<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Modern Interview UI - Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      .card-shadow {
        box-shadow:
          0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      .progress-circle {
        transform: rotate(-90deg);
      }
    </style>
  </head>
  <body class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl space-y-8 p-8">
      <!-- Header -->
      <div class="mb-12 text-center">
        <h1 class="mb-4 text-4xl font-bold text-gray-900">Modern Interview UI Design</h1>
        <p class="text-xl text-gray-600">Interviewer-focused design for better decision making</p>
      </div>

      <!-- Hero Section -->
      <div class="card-shadow overflow-hidden rounded-lg bg-white">
        <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8">
          <div class="flex flex-col items-start gap-8 lg:flex-row lg:items-center">
            <!-- Left side - Candidate info -->
            <div class="flex flex-1 items-center gap-6">
              <!-- Avatar -->
              <div
                class="flex h-20 w-20 items-center justify-center rounded-full border-4 border-white bg-gradient-to-br from-blue-500 to-purple-600 text-2xl font-bold text-white shadow-lg"
              >
                SK
              </div>

              <!-- Candidate details -->
              <div class="flex-1">
                <h1 class="mb-2 text-3xl font-bold text-gray-900">Shreeram Kodlekere</h1>
                <p class="mb-3 flex items-center gap-2 text-gray-600">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    ></path>
                  </svg>
                  <EMAIL>
                </p>

                <!-- Role and Level -->
                <div class="mb-4 flex flex-wrap gap-3">
                  <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800"
                    >Senior Software Engineer</span
                  >
                  <span class="rounded-full border border-gray-300 px-3 py-1 text-sm text-gray-700"
                    >Senior Level</span
                  >
                </div>

                <!-- Recommendation and Risk -->
                <div class="flex flex-wrap gap-3">
                  <span
                    class="flex items-center gap-2 rounded-full border border-green-200 bg-green-100 px-4 py-2 text-sm font-semibold text-green-800"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                    RECOMMENDED FOR HIRE
                  </span>
                  <span
                    class="flex items-center gap-2 rounded-full border border-green-200 bg-green-100 px-3 py-1 text-sm font-medium text-green-800"
                  >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      ></path>
                    </svg>
                    LOW RISK
                  </span>
                </div>
              </div>
            </div>

            <!-- Right side - Score and metadata -->
            <div class="flex flex-col items-center gap-8 lg:flex-row">
              <!-- Overall Score -->
              <div class="text-center">
                <div class="relative mb-2 h-32 w-32">
                  <svg class="progress-circle h-32 w-32" viewBox="0 0 36 36">
                    <path
                      class="text-gray-200"
                      stroke="currentColor"
                      stroke-width="3"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    ></path>
                    <path
                      class="text-blue-600"
                      stroke="currentColor"
                      stroke-width="3"
                      fill="none"
                      stroke-dasharray="85, 100"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    ></path>
                  </svg>
                  <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-2xl font-bold text-gray-900">8.5</span>
                  </div>
                </div>
                <p class="text-sm font-medium text-gray-600">Overall Score</p>
              </div>

              <!-- Metadata -->
              <div class="space-y-3 text-sm">
                <div class="flex items-center gap-2 text-gray-600">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <span>Completed: Jan 15, 2024</span>
                </div>

                <div class="flex items-center gap-2 text-gray-600">
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                  <span>Duration: 90 min</span>
                </div>

                <div class="flex items-center gap-2 text-gray-600">
                  <span class="h-4 w-4 text-center">📋</span>
                  <span>Event: Software Engineer Interview</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Key Metrics Dashboard -->
      <div class="space-y-6">
        <div class="flex items-center gap-3">
          <div class="rounded-lg bg-blue-100 p-2">
            <svg
              class="h-6 w-6 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
              ></path>
            </svg>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Key Performance Metrics</h2>
            <p class="text-gray-600">Overview of candidate's performance across different areas</p>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <!-- Technical Skills -->
          <div class="card-shadow overflow-hidden rounded-lg bg-white">
            <div class="h-1 bg-blue-500"></div>
            <div class="p-4">
              <div class="mb-3 flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-600">Technical Skills</h3>
                <div class="rounded-lg bg-blue-100 p-2">
                  <svg
                    class="h-5 w-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="mb-2 flex items-baseline gap-2">
                <span class="text-2xl font-bold text-gray-900">9.0</span>
                <span class="text-sm text-gray-500">/ 10</span>
              </div>
              <div class="mb-2 h-2 w-full rounded-full bg-gray-200">
                <div class="h-2 rounded-full bg-blue-500" style="width: 90%"></div>
              </div>
              <p class="text-xs text-gray-500">Coding & problem solving</p>
            </div>
          </div>

          <!-- Communication -->
          <div class="card-shadow overflow-hidden rounded-lg bg-white">
            <div class="h-1 bg-green-500"></div>
            <div class="p-4">
              <div class="mb-3 flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-600">Communication</h3>
                <div class="rounded-lg bg-green-100 p-2">
                  <svg
                    class="h-5 w-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="mb-2 flex items-baseline gap-2">
                <span class="text-2xl font-bold text-gray-900">8.0</span>
                <span class="text-sm text-gray-500">/ 10</span>
              </div>
              <div class="mb-2 h-2 w-full rounded-full bg-gray-200">
                <div class="h-2 rounded-full bg-green-500" style="width: 80%"></div>
              </div>
              <p class="text-xs text-gray-500">Clarity & articulation</p>
            </div>
          </div>

          <!-- Problem Solving -->
          <div class="card-shadow overflow-hidden rounded-lg bg-white">
            <div class="h-1 bg-purple-500"></div>
            <div class="p-4">
              <div class="mb-3 flex items-center justify-between">
                <h3 class="text-sm font-medium text-gray-600">Problem Solving</h3>
                <div class="rounded-lg bg-purple-100 p-2">
                  <svg
                    class="h-5 w-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="mb-2 flex items-baseline gap-2">
                <span class="text-2xl font-bold text-gray-900">8.5</span>
                <span class="text-sm text-gray-500">/ 10</span>
              </div>
              <div class="mb-2 h-2 w-full rounded-full bg-gray-200">
                <div class="h-2 rounded-full bg-purple-500" style="width: 85%"></div>
              </div>
              <p class="text-xs text-gray-500">Logical reasoning</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Executive Summary -->
      <div class="space-y-6">
        <div class="flex items-center gap-3">
          <div class="rounded-lg bg-purple-100 p-2">
            <svg
              class="h-6 w-6 text-purple-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              ></path>
            </svg>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Executive Summary</h2>
            <p class="text-gray-600">
              Key insights and recommendations from the interview assessment
            </p>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- Strengths -->
          <div class="card-shadow rounded-lg border border-green-200 bg-green-50">
            <div class="p-6">
              <div class="mb-4 flex items-center gap-3">
                <div class="rounded-lg bg-green-100 p-2">
                  <svg
                    class="h-5 w-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                    ></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-green-800">Key Strengths</h3>
              </div>
              <ul class="space-y-2">
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>Excellent technical knowledge in React and Node.js</span>
                </li>
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>Strong problem-solving approach with clean code</span>
                </li>
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>Good communication and explanation skills</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Areas for Improvement -->
          <div class="card-shadow rounded-lg border border-orange-200 bg-orange-50">
            <div class="p-6">
              <div class="mb-4 flex items-center gap-3">
                <div class="rounded-lg bg-orange-100 p-2">
                  <svg
                    class="h-5 w-5 text-orange-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                    ></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-orange-800">Areas for Improvement</h3>
              </div>
              <ul class="space-y-2">
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>System design knowledge for large-scale applications</span>
                </li>
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>More experience with microservices architecture</span>
                </li>
                <li class="flex items-start gap-2 text-gray-700">
                  <svg
                    class="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                  <span>Performance optimization techniques</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Complete Field Coverage -->
      <div class="mb-6 rounded-lg border border-green-200 bg-green-50 p-6">
        <h3 class="mb-3 text-lg font-semibold text-green-900">📋 Complete Field Coverage</h3>
        <p class="mb-4 text-green-800">
          The new modern UI now covers ALL existing fields from the original
          ViewInterviewDetailScreen:
        </p>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <h4 class="mb-2 font-semibold text-green-800">Hero Section</h4>
            <ul class="space-y-1 text-sm text-green-700">
              <li>• Candidate name & email</li>
              <li>• Overall score & recommendation</li>
              <li>• Risk level & proctoring status</li>
              <li>• Event, role, level details</li>
              <li>• Timing information</li>
              <li>• Interviewathon type indicator</li>
            </ul>
          </div>
          <div>
            <h4 class="mb-2 font-semibold text-green-800">Metrics Dashboard</h4>
            <ul class="space-y-1 text-sm text-green-700">
              <li>• Technical skills breakdown</li>
              <li>• Communication scores</li>
              <li>• Problem solving metrics</li>
              <li>• Questions completion rate</li>
              <li>• Core values assessment</li>
              <li>• Job fit analysis</li>
              <li>• Feedback generation status</li>
              <li>• Video recordings count</li>
            </ul>
          </div>
          <div>
            <h4 class="mb-2 font-semibold text-green-800">Sidebar & Details</h4>
            <ul class="space-y-1 text-sm text-green-700">
              <li>• Quick action buttons</li>
              <li>• Video interview details</li>
              <li>• Interviewer information</li>
              <li>• Proctoring warnings</li>
              <li>• Notes & comments system</li>
              <li>• Candidate profile summary</li>
              <li>• Resume access</li>
              <li>• Meeting recordings</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Comparison Note -->
      <div class="rounded-lg border border-blue-200 bg-blue-50 p-6">
        <h3 class="mb-3 text-lg font-semibold text-blue-900">
          🎯 Key Improvements in the Modern Design
        </h3>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <ul class="space-y-2 text-blue-800">
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span
                ><strong>Immediate Decision Support:</strong> Hire/Don't Hire recommendation
                prominently displayed</span
              >
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span><strong>Visual Hierarchy:</strong> Most important information at the top</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span
                ><strong>Scannable Metrics:</strong> Key performance indicators in dashboard
                format</span
              >
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span
                ><strong>Progressive Disclosure:</strong> Detailed analysis in expandable
                sections</span
              >
            </li>
          </ul>
          <ul class="space-y-2 text-blue-800">
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span><strong>Executive Summary:</strong> Organized strengths and improvements</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span><strong>Modern Aesthetics:</strong> Clean design with proper spacing</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span
                ><strong>Complete Field Coverage:</strong> All existing data now beautifully
                displayed</span
              >
            </li>
            <li class="flex items-start gap-2">
              <span class="font-bold text-green-600">✓</span>
              <span
                ><strong>Error-Free Implementation:</strong> Fixed all TypeScript and runtime
                errors</span
              >
            </li>
          </ul>
        </div>
      </div>
    </div>
  </body>
</html>
