<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Interview UI - Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .progress-circle {
            transform: rotate(-90deg);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto p-8 space-y-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Modern Interview UI Design</h1>
            <p class="text-xl text-gray-600">Interviewer-focused design for better decision making</p>
        </div>

        <!-- Hero Section -->
        <div class="bg-white rounded-lg card-shadow overflow-hidden">
            <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8">
                <div class="flex flex-col lg:flex-row items-start lg:items-center gap-8">
                    <!-- Left side - Candidate info -->
                    <div class="flex items-center gap-6 flex-1">
                        <!-- Avatar -->
                        <div class="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold border-4 border-white shadow-lg">
                            SK
                        </div>
                        
                        <!-- Candidate details -->
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">Shreeram Kodlekere</h1>
                            <p class="text-gray-600 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <EMAIL>
                            </p>
                            
                            <!-- Role and Level -->
                            <div class="flex flex-wrap gap-3 mb-4">
                                <span class="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">Senior Software Engineer</span>
                                <span class="border border-gray-300 text-gray-700 text-sm px-3 py-1 rounded-full">Senior Level</span>
                            </div>

                            <!-- Recommendation and Risk -->
                            <div class="flex flex-wrap gap-3">
                                <span class="bg-green-100 text-green-800 border border-green-200 text-sm font-semibold px-4 py-2 rounded-full flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    RECOMMENDED FOR HIRE
                                </span>
                                <span class="bg-green-100 text-green-800 border border-green-200 text-sm font-medium px-3 py-1 rounded-full flex items-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    LOW RISK
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Right side - Score and metadata -->
                    <div class="flex flex-col lg:flex-row items-center gap-8">
                        <!-- Overall Score -->
                        <div class="text-center">
                            <div class="relative w-32 h-32 mb-2">
                                <svg class="w-32 h-32 progress-circle" viewBox="0 0 36 36">
                                    <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-blue-600" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="85, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-2xl font-bold text-gray-900">8.5</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-gray-600">Overall Score</p>
                        </div>

                        <!-- Metadata -->
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center gap-2 text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span>Completed: Jan 15, 2024</span>
                            </div>
                            
                            <div class="flex items-center gap-2 text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Duration: 90 min</span>
                            </div>

                            <div class="flex items-center gap-2 text-gray-600">
                                <span class="w-4 h-4 text-center">📋</span>
                                <span>Event: Software Engineer Interview</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics Dashboard -->
        <div class="space-y-6">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Key Performance Metrics</h2>
                    <p class="text-gray-600">Overview of candidate's performance across different areas</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Technical Skills -->
                <div class="bg-white rounded-lg card-shadow overflow-hidden">
                    <div class="bg-blue-500 h-1"></div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-600">Technical Skills</h3>
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-baseline gap-2 mb-2">
                            <span class="text-2xl font-bold text-gray-900">9.0</span>
                            <span class="text-sm text-gray-500">/ 10</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 90%"></div>
                        </div>
                        <p class="text-xs text-gray-500">Coding & problem solving</p>
                    </div>
                </div>

                <!-- Communication -->
                <div class="bg-white rounded-lg card-shadow overflow-hidden">
                    <div class="bg-green-500 h-1"></div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-600">Communication</h3>
                            <div class="p-2 bg-green-100 rounded-lg">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-baseline gap-2 mb-2">
                            <span class="text-2xl font-bold text-gray-900">8.0</span>
                            <span class="text-sm text-gray-500">/ 10</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                        </div>
                        <p class="text-xs text-gray-500">Clarity & articulation</p>
                    </div>
                </div>

                <!-- Problem Solving -->
                <div class="bg-white rounded-lg card-shadow overflow-hidden">
                    <div class="bg-purple-500 h-1"></div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-600">Problem Solving</h3>
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-baseline gap-2 mb-2">
                            <span class="text-2xl font-bold text-gray-900">8.5</span>
                            <span class="text-sm text-gray-500">/ 10</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                        <p class="text-xs text-gray-500">Logical reasoning</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="space-y-6">
            <div class="flex items-center gap-3">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Executive Summary</h2>
                    <p class="text-gray-600">Key insights and recommendations from the interview assessment</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Strengths -->
                <div class="bg-green-50 border border-green-200 rounded-lg card-shadow">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-green-800">Key Strengths</h3>
                        </div>
                        <ul class="space-y-2">
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>Excellent technical knowledge in React and Node.js</span>
                            </li>
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>Strong problem-solving approach with clean code</span>
                            </li>
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>Good communication and explanation skills</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Areas for Improvement -->
                <div class="bg-orange-50 border border-orange-200 rounded-lg card-shadow">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-4">
                            <div class="p-2 bg-orange-100 rounded-lg">
                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-orange-800">Areas for Improvement</h3>
                        </div>
                        <ul class="space-y-2">
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>System design knowledge for large-scale applications</span>
                            </li>
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>More experience with microservices architecture</span>
                            </li>
                            <li class="flex items-start gap-2 text-gray-700">
                                <svg class="w-4 h-4 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span>Performance optimization techniques</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Note -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">🎯 Key Improvements in the Modern Design</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ul class="space-y-2 text-blue-800">
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Immediate Decision Support:</strong> Hire/Don't Hire recommendation prominently displayed</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Visual Hierarchy:</strong> Most important information at the top</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Scannable Metrics:</strong> Key performance indicators in dashboard format</span>
                    </li>
                </ul>
                <ul class="space-y-2 text-blue-800">
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Executive Summary:</strong> Organized strengths and improvements</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Modern Aesthetics:</strong> Clean design with proper spacing</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-green-600 font-bold">✓</span>
                        <span><strong>Progressive Disclosure:</strong> Detailed analysis in expandable sections</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
